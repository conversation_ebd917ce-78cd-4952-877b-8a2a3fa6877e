"""
Excel database handler for user data and usage logs
"""
import pandas as pd
import os
from datetime import datetime, date
from typing import Dict, List, Optional, Any
from utils.logger import logger

class ExcelHandler:
    """Handle Excel database operations"""
    
    def __init__(self, file_path: str):
        self.file_path = file_path
        self.ensure_file_exists()
    
    def ensure_file_exists(self):
        """Create Excel file if it doesn't exist"""
        if not os.path.exists(self.file_path):
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(self.file_path), exist_ok=True)
            
            # Create empty DataFrame based on file type
            if 'users' in self.file_path:
                df = pd.DataFrame(columns=[
                    'telegram_id', 'username', 'first_name', 'last_name',
                    'registration_date', 'last_activity', 'usage_count',
                    'current_plan', 'is_active', 'is_banned'
                ])
            elif 'usage_logs' in self.file_path:
                df = pd.DataFrame(columns=[
                    'telegram_id', 'action', 'timestamp', 'details'
                ])
            else:
                df = pd.DataFrame()
            
            df.to_excel(self.file_path, index=False)
            logger.info(f"Created new Excel file: {self.file_path}")
    
    def read_data(self) -> pd.DataFrame:
        """Read data from Excel file"""
        try:
            return pd.read_excel(self.file_path)
        except Exception as e:
            logger.error(f"Error reading Excel file {self.file_path}: {e}")
            return pd.DataFrame()
    
    def write_data(self, df: pd.DataFrame) -> bool:
        """Write data to Excel file"""
        try:
            df.to_excel(self.file_path, index=False)
            logger.info(f"Data written to {self.file_path}")
            return True
        except Exception as e:
            logger.error(f"Error writing to Excel file {self.file_path}: {e}")
            return False
    
    def append_row(self, data: Dict[str, Any]) -> bool:
        """Append a new row to the Excel file"""
        try:
            df = self.read_data()
            new_row = pd.DataFrame([data])
            df = pd.concat([df, new_row], ignore_index=True)
            return self.write_data(df)
        except Exception as e:
            logger.error(f"Error appending row to {self.file_path}: {e}")
            return False
    
    def update_row(self, condition: Dict[str, Any], updates: Dict[str, Any]) -> bool:
        """Update rows matching condition"""
        try:
            df = self.read_data()
            
            # Create boolean mask for condition
            mask = pd.Series([True] * len(df))
            for key, value in condition.items():
                if key in df.columns:
                    mask &= (df[key] == value)
            
            # Update matching rows
            for key, value in updates.items():
                if key in df.columns:
                    df.loc[mask, key] = value
            
            return self.write_data(df)
        except Exception as e:
            logger.error(f"Error updating rows in {self.file_path}: {e}")
            return False
    
    def delete_rows(self, condition: Dict[str, Any]) -> bool:
        """Delete rows matching condition"""
        try:
            df = self.read_data()
            
            # Create boolean mask for condition
            mask = pd.Series([True] * len(df))
            for key, value in condition.items():
                if key in df.columns:
                    mask &= (df[key] == value)
            
            # Keep rows that don't match the condition
            df = df[~mask]
            return self.write_data(df)
        except Exception as e:
            logger.error(f"Error deleting rows from {self.file_path}: {e}")
            return False
    
    def search_rows(self, condition: Dict[str, Any]) -> pd.DataFrame:
        """Search for rows matching condition"""
        try:
            df = self.read_data()
            
            # Create boolean mask for condition
            mask = pd.Series([True] * len(df))
            for key, value in condition.items():
                if key in df.columns:
                    if isinstance(value, str):
                        # Case-insensitive string search
                        mask &= df[key].astype(str).str.contains(value, case=False, na=False)
                    else:
                        mask &= (df[key] == value)
            
            return df[mask]
        except Exception as e:
            logger.error(f"Error searching rows in {self.file_path}: {e}")
            return pd.DataFrame()
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get basic statistics from the data"""
        try:
            df = self.read_data()
            
            if 'users' in self.file_path:
                return {
                    'total_users': len(df),
                    'active_users': len(df[df.get('is_active', True) == True]),
                    'banned_users': len(df[df.get('is_banned', False) == True]),
                    'plans': df.get('current_plan', pd.Series()).value_counts().to_dict()
                }
            elif 'usage_logs' in self.file_path:
                return {
                    'total_actions': len(df),
                    'unique_users': df['telegram_id'].nunique() if 'telegram_id' in df.columns else 0,
                    'actions_by_type': df.get('action', pd.Series()).value_counts().to_dict()
                }
            else:
                return {'total_rows': len(df)}
                
        except Exception as e:
            logger.error(f"Error getting statistics from {self.file_path}: {e}")
            return {}
