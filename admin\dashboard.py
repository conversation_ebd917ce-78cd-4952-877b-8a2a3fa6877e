"""
Admin dashboard for user management and statistics
"""
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from database.user_manager import UserManager
from utils.logger import logger
import pandas as pd

class AdminDashboard:
    """Admin dashboard functionality"""
    
    def __init__(self, user_manager: UserManager):
        self.user_manager = user_manager
    
    def get_user_statistics(self) -> Dict[str, Any]:
        """Get comprehensive user statistics"""
        try:
            stats = self.user_manager.get_statistics()
            
            # Get additional metrics
            all_users = self.user_manager.get_all_users()
            
            # Calculate additional metrics
            today = datetime.now().date()
            week_ago = today - timedelta(days=7)
            month_ago = today - timedelta(days=30)
            
            new_users_today = 0
            new_users_week = 0
            new_users_month = 0
            active_users_today = 0
            
            for user in all_users:
                reg_date_str = user.get('registration_date', '')
                last_activity_str = user.get('last_activity', '')
                
                try:
                    reg_date = datetime.strptime(reg_date_str, '%Y-%m-%d %H:%M:%S').date()
                    if reg_date == today:
                        new_users_today += 1
                    if reg_date >= week_ago:
                        new_users_week += 1
                    if reg_date >= month_ago:
                        new_users_month += 1
                except:
                    pass
                
                try:
                    last_activity = datetime.strptime(last_activity_str, '%Y-%m-%d %H:%M:%S').date()
                    if last_activity == today:
                        active_users_today += 1
                except:
                    pass
            
            enhanced_stats = {
                'basic_stats': stats.get('users', {}),
                'usage_stats': stats.get('usage', {}),
                'growth_metrics': {
                    'new_users_today': new_users_today,
                    'new_users_this_week': new_users_week,
                    'new_users_this_month': new_users_month,
                    'active_users_today': active_users_today
                },
                'generated_at': stats.get('generated_at', datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            }
            
            return enhanced_stats
            
        except Exception as e:
            logger.error(f"Error getting user statistics: {e}")
            return {}
    
    def get_user_list(self, page: int = 1, per_page: int = 10) -> Dict[str, Any]:
        """Get paginated user list"""
        try:
            all_users = self.user_manager.get_all_users()
            
            # Sort by registration date (newest first)
            all_users.sort(key=lambda x: x.get('registration_date', ''), reverse=True)
            
            # Paginate
            start_idx = (page - 1) * per_page
            end_idx = start_idx + per_page
            users_page = all_users[start_idx:end_idx]
            
            return {
                'users': users_page,
                'pagination': {
                    'current_page': page,
                    'per_page': per_page,
                    'total_users': len(all_users),
                    'total_pages': (len(all_users) + per_page - 1) // per_page,
                    'has_next': end_idx < len(all_users),
                    'has_prev': page > 1
                }
            }
            
        except Exception as e:
            logger.error(f"Error getting user list: {e}")
            return {'users': [], 'pagination': {}}
    
    def search_users(self, query: str) -> List[Dict[str, Any]]:
        """Search users by various criteria"""
        try:
            return self.user_manager.search_users(query)
        except Exception as e:
            logger.error(f"Error searching users: {e}")
            return []
    
    def get_user_details(self, telegram_id: int) -> Optional[Dict[str, Any]]:
        """Get detailed user information"""
        try:
            user = self.user_manager.get_user(telegram_id)
            if not user:
                return None
            
            # Get user's usage logs
            usage_logs = self.user_manager.usage_db.search_rows({'telegram_id': telegram_id})
            
            # Calculate additional metrics
            total_actions = len(usage_logs)
            recent_actions = usage_logs.tail(10).to_dict('records') if not usage_logs.empty else []
            
            # Get action breakdown
            action_breakdown = {}
            if not usage_logs.empty:
                action_breakdown = usage_logs['action'].value_counts().to_dict()
            
            return {
                'user_info': user,
                'usage_metrics': {
                    'total_actions': total_actions,
                    'daily_usage': self.user_manager.get_daily_usage(telegram_id),
                    'action_breakdown': action_breakdown
                },
                'recent_actions': recent_actions
            }
            
        except Exception as e:
            logger.error(f"Error getting user details: {e}")
            return None
    
    def update_user_plan(self, telegram_id: int, new_plan: str) -> bool:
        """Update user's subscription plan"""
        try:
            success = self.user_manager.change_user_plan(telegram_id, new_plan)
            if success:
                logger.info(f"Admin updated user {telegram_id} plan to {new_plan}")
            return success
        except Exception as e:
            logger.error(f"Error updating user plan: {e}")
            return False
    
    def ban_user(self, telegram_id: int, reason: str = "Admin action") -> bool:
        """Ban a user"""
        try:
            success = self.user_manager.ban_user(telegram_id, reason)
            if success:
                logger.info(f"Admin banned user {telegram_id}: {reason}")
            return success
        except Exception as e:
            logger.error(f"Error banning user: {e}")
            return False
    
    def unban_user(self, telegram_id: int) -> bool:
        """Unban a user"""
        try:
            success = self.user_manager.unban_user(telegram_id)
            if success:
                logger.info(f"Admin unbanned user {telegram_id}")
            return success
        except Exception as e:
            logger.error(f"Error unbanning user: {e}")
            return False
    
    def get_banned_users(self) -> List[Dict[str, Any]]:
        """Get list of banned users"""
        try:
            all_users = self.user_manager.get_all_users()
            banned_users = [user for user in all_users if user.get('is_banned', False)]
            return banned_users
        except Exception as e:
            logger.error(f"Error getting banned users: {e}")
            return []
    
    def get_usage_analytics(self, days: int = 30) -> Dict[str, Any]:
        """Get usage analytics for the specified period"""
        try:
            # Get usage logs
            usage_logs = self.user_manager.usage_db.read_data()
            
            if usage_logs.empty:
                return {'error': 'No usage data available'}
            
            # Filter by date range
            cutoff_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')
            recent_logs = usage_logs[usage_logs['timestamp'] >= cutoff_date]
            
            # Calculate analytics
            analytics = {
                'period_days': days,
                'total_actions': len(recent_logs),
                'unique_users': recent_logs['telegram_id'].nunique() if not recent_logs.empty else 0,
                'actions_by_type': recent_logs['action'].value_counts().to_dict() if not recent_logs.empty else {},
                'daily_usage': {},
                'top_users': {}
            }
            
            # Daily usage breakdown
            if not recent_logs.empty:
                recent_logs['date'] = pd.to_datetime(recent_logs['timestamp']).dt.date
                daily_usage = recent_logs.groupby('date').size().to_dict()
                analytics['daily_usage'] = {str(k): v for k, v in daily_usage.items()}
                
                # Top users by activity
                top_users = recent_logs['telegram_id'].value_counts().head(10).to_dict()
                analytics['top_users'] = top_users
            
            return analytics
            
        except Exception as e:
            logger.error(f"Error getting usage analytics: {e}")
            return {'error': str(e)}
    
    def export_user_data(self, format: str = 'excel') -> str:
        """Export user data to file"""
        try:
            all_users = self.user_manager.get_all_users()
            
            if not all_users:
                return ""
            
            df = pd.DataFrame(all_users)
            
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            
            if format.lower() == 'excel':
                filename = f"user_export_{timestamp}.xlsx"
                filepath = f"exports/{filename}"
                df.to_excel(filepath, index=False)
            elif format.lower() == 'csv':
                filename = f"user_export_{timestamp}.csv"
                filepath = f"exports/{filename}"
                df.to_csv(filepath, index=False)
            else:
                return ""
            
            logger.info(f"User data exported to {filepath}")
            return filepath
            
        except Exception as e:
            logger.error(f"Error exporting user data: {e}")
            return ""
    
    def get_system_health(self) -> Dict[str, Any]:
        """Get system health metrics"""
        try:
            import psutil
            import os
            
            # Get system metrics
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            # Get database file sizes
            users_db_size = os.path.getsize(self.user_manager.users_db.file_path) if os.path.exists(self.user_manager.users_db.file_path) else 0
            usage_db_size = os.path.getsize(self.user_manager.usage_db.file_path) if os.path.exists(self.user_manager.usage_db.file_path) else 0
            
            return {
                'system_metrics': {
                    'cpu_usage_percent': cpu_percent,
                    'memory_usage_percent': memory.percent,
                    'memory_available_gb': memory.available / (1024**3),
                    'disk_usage_percent': disk.percent,
                    'disk_free_gb': disk.free / (1024**3)
                },
                'database_metrics': {
                    'users_db_size_mb': users_db_size / (1024**2),
                    'usage_db_size_mb': usage_db_size / (1024**2),
                    'total_db_size_mb': (users_db_size + usage_db_size) / (1024**2)
                },
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
        except ImportError:
            return {
                'error': 'psutil not installed - system metrics unavailable',
                'database_metrics': {
                    'users_db_exists': os.path.exists(self.user_manager.users_db.file_path),
                    'usage_db_exists': os.path.exists(self.user_manager.usage_db.file_path)
                }
            }
        except Exception as e:
            logger.error(f"Error getting system health: {e}")
            return {'error': str(e)}
    
    def cleanup_old_data(self, days_old: int = 90) -> Dict[str, int]:
        """Clean up old usage logs"""
        try:
            cutoff_date = (datetime.now() - timedelta(days=days_old)).strftime('%Y-%m-%d')
            
            # Get usage logs
            usage_logs = self.user_manager.usage_db.read_data()
            
            if usage_logs.empty:
                return {'deleted_records': 0}
            
            # Filter old records
            old_records = usage_logs[usage_logs['timestamp'] < cutoff_date]
            records_to_delete = len(old_records)
            
            # Keep recent records
            recent_records = usage_logs[usage_logs['timestamp'] >= cutoff_date]
            
            # Update database
            success = self.user_manager.usage_db.write_data(recent_records)
            
            if success:
                logger.info(f"Cleaned up {records_to_delete} old usage records")
                return {'deleted_records': records_to_delete}
            else:
                return {'deleted_records': 0, 'error': 'Failed to update database'}
                
        except Exception as e:
            logger.error(f"Error cleaning up old data: {e}")
            return {'deleted_records': 0, 'error': str(e)}
