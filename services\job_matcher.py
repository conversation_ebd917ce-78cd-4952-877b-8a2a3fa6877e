"""
Job-CV matching service using AI analysis
"""
from typing import Dict, List, Any, Optional
from services.ai_analyzer import AIAnalyzer
from utils.logger import logger
import pandas as pd
import os

class JobMatcher:
    """Match jobs with CV using AI analysis"""
    
    def __init__(self, ai_analyzer: AIAnalyzer):
        self.ai_analyzer = ai_analyzer
        self.exports_dir = "exports"
        os.makedirs(self.exports_dir, exist_ok=True)
    
    def match_cv_with_jobs(self, cv_text: str, jobs: List[Dict[str, str]]) -> List[Dict[str, Any]]:
        """
        Match CV with job listings and calculate compatibility scores
        
        Args:
            cv_text: Extracted CV text
            jobs: List of job dictionaries
            
        Returns:
            List of jobs with compatibility scores and analysis
        """
        try:
            if not cv_text or not jobs:
                logger.warning("Empty CV text or jobs list provided")
                return []
            
            logger.info(f"Matching CV with {len(jobs)} jobs")
            
            # Use AI analyzer to match jobs
            matched_jobs = self.ai_analyzer.match_jobs_to_cv(cv_text, jobs)
            
            # Add additional matching metrics
            enhanced_matches = []
            for job in matched_jobs:
                enhanced_job = self._enhance_job_match(job, cv_text)
                enhanced_matches.append(enhanced_job)
            
            # Sort by compatibility score
            enhanced_matches.sort(key=lambda x: x.get('compatibility_score', 0), reverse=True)
            
            logger.info(f"Job matching completed. Top score: {enhanced_matches[0].get('compatibility_score', 0) if enhanced_matches else 0}")
            
            return enhanced_matches
            
        except Exception as e:
            logger.error(f"Error matching CV with jobs: {e}")
            return jobs  # Return original jobs if matching fails
    
    def _enhance_job_match(self, job: Dict[str, Any], cv_text: str) -> Dict[str, Any]:
        """Enhance job match with additional analysis"""
        try:
            enhanced_job = job.copy()
            
            # Add match categories
            enhanced_job['match_categories'] = self._categorize_match(job, cv_text)
            
            # Add recommendation level
            score = job.get('compatibility_score', 0)
            enhanced_job['recommendation_level'] = self._get_recommendation_level(score)
            
            # Add application priority
            enhanced_job['application_priority'] = self._calculate_priority(job, cv_text)
            
            return enhanced_job
            
        except Exception as e:
            logger.error(f"Error enhancing job match: {e}")
            return job
    
    def _categorize_match(self, job: Dict[str, Any], cv_text: str) -> Dict[str, str]:
        """Categorize the type of match"""
        try:
            categories = {
                'skill_match': 'Unknown',
                'experience_match': 'Unknown',
                'location_match': 'Unknown',
                'industry_match': 'Unknown'
            }
            
            score = job.get('compatibility_score', 0)
            matching_skills = job.get('matching_skills', [])
            missing_skills = job.get('missing_skills', [])
            
            # Skill match assessment
            if len(matching_skills) > len(missing_skills):
                categories['skill_match'] = 'Strong'
            elif len(matching_skills) == len(missing_skills):
                categories['skill_match'] = 'Moderate'
            else:
                categories['skill_match'] = 'Weak'
            
            # Experience match (simplified)
            if score >= 80:
                categories['experience_match'] = 'Excellent'
            elif score >= 60:
                categories['experience_match'] = 'Good'
            elif score >= 40:
                categories['experience_match'] = 'Fair'
            else:
                categories['experience_match'] = 'Limited'
            
            # Location match
            job_location = job.get('location', '').lower()
            if 'remote' in job_location or 'remote' in cv_text.lower():
                categories['location_match'] = 'Remote Friendly'
            else:
                categories['location_match'] = 'Location Specific'
            
            return categories
            
        except Exception as e:
            logger.error(f"Error categorizing match: {e}")
            return {}
    
    def _get_recommendation_level(self, score: int) -> str:
        """Get recommendation level based on compatibility score"""
        if score >= 85:
            return "Highly Recommended"
        elif score >= 70:
            return "Recommended"
        elif score >= 55:
            return "Consider Applying"
        elif score >= 40:
            return "Possible Match"
        else:
            return "Low Match"
    
    def _calculate_priority(self, job: Dict[str, Any], cv_text: str) -> str:
        """Calculate application priority"""
        try:
            score = job.get('compatibility_score', 0)
            matching_skills = len(job.get('matching_skills', []))
            missing_skills = len(job.get('missing_skills', []))
            
            # Priority calculation logic
            if score >= 80 and matching_skills > missing_skills:
                return "High Priority"
            elif score >= 60:
                return "Medium Priority"
            else:
                return "Low Priority"
                
        except Exception as e:
            logger.error(f"Error calculating priority: {e}")
            return "Unknown Priority"
    
    def get_top_matches(self, matched_jobs: List[Dict[str, Any]], count: int = 3) -> List[Dict[str, Any]]:
        """Get top N job matches"""
        try:
            # Sort by compatibility score
            sorted_jobs = sorted(
                matched_jobs, 
                key=lambda x: x.get('compatibility_score', 0), 
                reverse=True
            )
            
            return sorted_jobs[:count]
            
        except Exception as e:
            logger.error(f"Error getting top matches: {e}")
            return matched_jobs[:count]
    
    def generate_match_report(self, matched_jobs: List[Dict[str, Any]], user_id: int) -> str:
        """Generate a detailed matching report"""
        try:
            if not matched_jobs:
                return ""
            
            # Create DataFrame for analysis
            df_data = []
            for job in matched_jobs:
                df_data.append({
                    'Job Title': job.get('title', 'Unknown'),
                    'Company': job.get('company', 'Unknown'),
                    'Location': job.get('location', 'Unknown'),
                    'Compatibility Score': job.get('compatibility_score', 0),
                    'Recommendation': job.get('recommendation_level', 'Unknown'),
                    'Priority': job.get('application_priority', 'Unknown'),
                    'Matching Skills': ', '.join(job.get('matching_skills', [])),
                    'Missing Skills': ', '.join(job.get('missing_skills', [])),
                    'Posted Date': job.get('posted_date', 'Unknown')
                })
            
            df = pd.DataFrame(df_data)
            
            # Save to Excel
            filename = f"job_match_report_{user_id}_{pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            file_path = os.path.join(self.exports_dir, filename)
            
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                # Main report
                df.to_excel(writer, sheet_name='Job Matches', index=False)
                
                # Summary statistics
                summary_data = {
                    'Metric': [
                        'Total Jobs Analyzed',
                        'Average Compatibility Score',
                        'High Priority Jobs',
                        'Recommended Jobs',
                        'Jobs with 80%+ Match'
                    ],
                    'Value': [
                        len(matched_jobs),
                        f"{df['Compatibility Score'].mean():.1f}%",
                        len(df[df['Priority'] == 'High Priority']),
                        len(df[df['Recommendation'].str.contains('Recommended', na=False)]),
                        len(df[df['Compatibility Score'] >= 80])
                    ]
                }
                
                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name='Summary', index=False)
                
                # Top matches
                top_matches = df.nlargest(5, 'Compatibility Score')
                top_matches.to_excel(writer, sheet_name='Top Matches', index=False)
            
            logger.info(f"Match report generated: {file_path}")
            return file_path
            
        except Exception as e:
            logger.error(f"Error generating match report: {e}")
            return ""
    
    def analyze_skill_gaps(self, cv_text: str, target_jobs: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze skill gaps based on target jobs"""
        try:
            all_missing_skills = []
            all_matching_skills = []
            
            for job in target_jobs:
                missing_skills = job.get('missing_skills', [])
                matching_skills = job.get('matching_skills', [])
                
                all_missing_skills.extend(missing_skills)
                all_matching_skills.extend(matching_skills)
            
            # Count skill frequencies
            missing_skill_counts = {}
            for skill in all_missing_skills:
                missing_skill_counts[skill] = missing_skill_counts.get(skill, 0) + 1
            
            matching_skill_counts = {}
            for skill in all_matching_skills:
                matching_skill_counts[skill] = matching_skill_counts.get(skill, 0) + 1
            
            # Get top missing skills
            top_missing = sorted(
                missing_skill_counts.items(), 
                key=lambda x: x[1], 
                reverse=True
            )[:10]
            
            # Get top matching skills
            top_matching = sorted(
                matching_skill_counts.items(), 
                key=lambda x: x[1], 
                reverse=True
            )[:10]
            
            return {
                'top_missing_skills': [{'skill': skill, 'frequency': count} for skill, count in top_missing],
                'top_matching_skills': [{'skill': skill, 'frequency': count} for skill, count in top_matching],
                'skill_gap_analysis': {
                    'total_missing_skills': len(set(all_missing_skills)),
                    'total_matching_skills': len(set(all_matching_skills)),
                    'skill_coverage_ratio': len(set(all_matching_skills)) / (len(set(all_missing_skills)) + len(set(all_matching_skills))) if (all_missing_skills or all_matching_skills) else 0
                }
            }
            
        except Exception as e:
            logger.error(f"Error analyzing skill gaps: {e}")
            return {}
    
    def get_improvement_suggestions(self, cv_text: str, matched_jobs: List[Dict[str, Any]]) -> List[str]:
        """Get CV improvement suggestions based on job matches"""
        try:
            suggestions = []
            
            # Analyze top jobs
            top_jobs = self.get_top_matches(matched_jobs, 5)
            
            # Get skill gap analysis
            skill_analysis = self.analyze_skill_gaps(cv_text, top_jobs)
            
            # Generate suggestions based on missing skills
            top_missing = skill_analysis.get('top_missing_skills', [])
            if top_missing:
                suggestions.append(f"Consider adding these in-demand skills: {', '.join([skill['skill'] for skill in top_missing[:5]])}")
            
            # Analyze compatibility scores
            avg_score = sum(job.get('compatibility_score', 0) for job in matched_jobs) / len(matched_jobs) if matched_jobs else 0
            
            if avg_score < 60:
                suggestions.append("Consider tailoring your CV to better match job requirements")
                suggestions.append("Add more specific keywords related to your target roles")
            
            if avg_score < 40:
                suggestions.append("Consider gaining additional skills or certifications in your field")
            
            # Check for common patterns
            high_priority_jobs = [job for job in matched_jobs if job.get('application_priority') == 'High Priority']
            if len(high_priority_jobs) < 3:
                suggestions.append("Focus on developing skills that appear frequently in job postings")
            
            return suggestions[:5]  # Return top 5 suggestions
            
        except Exception as e:
            logger.error(f"Error getting improvement suggestions: {e}")
            return ["Review and update your CV regularly", "Tailor your CV for specific job applications"]
