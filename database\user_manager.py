"""
User management system for the Telegram bot
"""
from datetime import datetime, date
from typing import Dict, List, Optional, Any
from database.excel_handler import ExcelHandler
from config.settings import Config
from utils.logger import logger

class UserManager:
    """Manage user data and operations"""
    
    def __init__(self):
        self.users_db = ExcelHandler(Config.USERS_DB_PATH)
        self.usage_db = ExcelHandler(Config.USAGE_LOGS_DB_PATH)
    
    def register_user(self, telegram_id: int, username: str = None, 
                     first_name: str = None, last_name: str = None) -> bool:
        """Register a new user"""
        try:
            # Check if user already exists
            existing_user = self.get_user(telegram_id)
            if existing_user is not None:
                logger.info(f"User {telegram_id} already registered")
                return True
            
            # Create new user record
            user_data = {
                'telegram_id': telegram_id,
                'username': username or '',
                'first_name': first_name or '',
                'last_name': last_name or '',
                'registration_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'last_activity': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'usage_count': 0,
                'current_plan': 'free',
                'is_active': True,
                'is_banned': False
            }
            
            success = self.users_db.append_row(user_data)
            if success:
                logger.info(f"User {telegram_id} registered successfully")
                self.log_usage(telegram_id, 'user_registration', 'New user registered')
            return success
            
        except Exception as e:
            logger.error(f"Error registering user {telegram_id}: {e}")
            return False
    
    def get_user(self, telegram_id: int) -> Optional[Dict[str, Any]]:
        """Get user data by telegram ID"""
        try:
            result = self.users_db.search_rows({'telegram_id': telegram_id})
            if not result.empty:
                return result.iloc[0].to_dict()
            return None
        except Exception as e:
            logger.error(f"Error getting user {telegram_id}: {e}")
            return None
    
    def update_user(self, telegram_id: int, updates: Dict[str, Any]) -> bool:
        """Update user data"""
        try:
            # Add last_activity timestamp
            updates['last_activity'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            success = self.users_db.update_row(
                {'telegram_id': telegram_id}, 
                updates
            )
            if success:
                logger.info(f"User {telegram_id} updated successfully")
            return success
        except Exception as e:
            logger.error(f"Error updating user {telegram_id}: {e}")
            return False
    
    def increment_usage(self, telegram_id: int) -> bool:
        """Increment user usage count"""
        try:
            user = self.get_user(telegram_id)
            if user:
                new_count = user.get('usage_count', 0) + 1
                return self.update_user(telegram_id, {'usage_count': new_count})
            return False
        except Exception as e:
            logger.error(f"Error incrementing usage for user {telegram_id}: {e}")
            return False
    
    def check_usage_limit(self, telegram_id: int) -> Dict[str, Any]:
        """Check if user has exceeded usage limit"""
        try:
            user = self.get_user(telegram_id)
            if not user:
                return {'allowed': False, 'reason': 'User not found'}
            
            if user.get('is_banned', False):
                return {'allowed': False, 'reason': 'User is banned'}
            
            if not user.get('is_active', True):
                return {'allowed': False, 'reason': 'User is inactive'}
            
            plan = user.get('current_plan', 'free')
            usage_count = user.get('usage_count', 0)
            limit = Config.USAGE_LIMITS.get(plan, 3)
            
            # Check daily usage for non-premium users
            if plan != 'premium':
                today_usage = self.get_daily_usage(telegram_id)
                if today_usage >= limit:
                    return {
                        'allowed': False, 
                        'reason': f'Daily limit exceeded ({today_usage}/{limit})',
                        'usage_count': today_usage,
                        'limit': limit
                    }
            
            return {
                'allowed': True, 
                'usage_count': usage_count,
                'limit': limit if plan != 'premium' else 'unlimited'
            }
            
        except Exception as e:
            logger.error(f"Error checking usage limit for user {telegram_id}: {e}")
            return {'allowed': False, 'reason': 'System error'}
    
    def get_daily_usage(self, telegram_id: int) -> int:
        """Get user's usage count for today"""
        try:
            today = date.today().strftime('%Y-%m-%d')
            logs = self.usage_db.search_rows({'telegram_id': telegram_id})
            
            if logs.empty:
                return 0
            
            # Filter logs for today
            today_logs = logs[logs['timestamp'].str.contains(today, na=False)]
            return len(today_logs)
            
        except Exception as e:
            logger.error(f"Error getting daily usage for user {telegram_id}: {e}")
            return 0
    
    def log_usage(self, telegram_id: int, action: str, details: str = '') -> bool:
        """Log user action"""
        try:
            log_data = {
                'telegram_id': telegram_id,
                'action': action,
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'details': details
            }
            
            return self.usage_db.append_row(log_data)
        except Exception as e:
            logger.error(f"Error logging usage for user {telegram_id}: {e}")
            return False
    
    def ban_user(self, telegram_id: int, reason: str = '') -> bool:
        """Ban a user"""
        try:
            success = self.update_user(telegram_id, {'is_banned': True})
            if success:
                self.log_usage(telegram_id, 'user_banned', reason)
                logger.info(f"User {telegram_id} banned: {reason}")
            return success
        except Exception as e:
            logger.error(f"Error banning user {telegram_id}: {e}")
            return False
    
    def unban_user(self, telegram_id: int) -> bool:
        """Unban a user"""
        try:
            success = self.update_user(telegram_id, {'is_banned': False})
            if success:
                self.log_usage(telegram_id, 'user_unbanned', '')
                logger.info(f"User {telegram_id} unbanned")
            return success
        except Exception as e:
            logger.error(f"Error unbanning user {telegram_id}: {e}")
            return False
    
    def change_user_plan(self, telegram_id: int, new_plan: str) -> bool:
        """Change user's subscription plan"""
        try:
            if new_plan not in Config.USAGE_LIMITS:
                return False
            
            success = self.update_user(telegram_id, {'current_plan': new_plan})
            if success:
                self.log_usage(telegram_id, 'plan_changed', f'Changed to {new_plan}')
                logger.info(f"User {telegram_id} plan changed to {new_plan}")
            return success
        except Exception as e:
            logger.error(f"Error changing plan for user {telegram_id}: {e}")
            return False
    
    def get_all_users(self) -> List[Dict[str, Any]]:
        """Get all users"""
        try:
            df = self.users_db.read_data()
            return df.to_dict('records')
        except Exception as e:
            logger.error(f"Error getting all users: {e}")
            return []
    
    def search_users(self, query: str) -> List[Dict[str, Any]]:
        """Search users by username, first name, or last name"""
        try:
            df = self.users_db.read_data()
            
            # Search in multiple columns
            mask = (
                df['username'].astype(str).str.contains(query, case=False, na=False) |
                df['first_name'].astype(str).str.contains(query, case=False, na=False) |
                df['last_name'].astype(str).str.contains(query, case=False, na=False)
            )
            
            return df[mask].to_dict('records')
        except Exception as e:
            logger.error(f"Error searching users: {e}")
            return []
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get user statistics"""
        try:
            user_stats = self.users_db.get_statistics()
            usage_stats = self.usage_db.get_statistics()
            
            return {
                'users': user_stats,
                'usage': usage_stats,
                'generated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
        except Exception as e:
            logger.error(f"Error getting statistics: {e}")
            return {}
