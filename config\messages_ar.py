"""
Arabic messages for the Telegram Job Bot
"""

class ArabicMessages:
    """Arabic text messages for the bot"""
    
    # Welcome and main menu
    WELCOME_MESSAGE = """
🤖 **أهلاً بك في بوت البحث الذكي عن الوظائف!**

مرحباً {name}! أنا هنا لمساعدتك في:

🔍 **البحث عن الوظائف** - ابحث عن وظائف في منصات متعددة
📄 **مساعد السيرة الذاتية** - إنشاء وتحليل وتعديل سيرتك الذاتية
🎯 **مطابقة الوظائف** - اعثر على الوظائف المناسبة لسيرتك الذاتية
📊 **التحليل بالذكاء الاصطناعي** - احصل على رؤى مدعومة بالذكاء الاصطناعي

اختر خياراً من الأسفل للبدء:
    """
    
    # Main menu buttons
    MAIN_MENU_BUTTONS = {
        'job_search': '🔍 البحث عن الوظائف',
        'cv_operations': '📄 مساعد السيرة الذاتية',
        'job_matching': '🎯 مطابقة الوظائف',
        'help': 'ℹ️ المساعدة',
        'admin_panel': '⚙️ لوحة الإدارة'
    }
    
    # Job search messages
    JOB_SEARCH_START = "🔍 **البحث عن الوظائف**\n\nاختر فئة الوظيفة:"
    JOB_SEARCH_COUNTRY = "📍 **الفئة المختارة:** {category}\n\nالآن اختر الدولة:"
    JOB_SEARCH_TYPE = "📍 **المختار:** {category} في {country}\n\nاختر نوع الوظيفة:"
    
    # Job categories in Arabic
    JOB_CATEGORIES_AR = {
        'Front End': '💻 تطوير الواجهات الأمامية',
        'Back End': '⚙️ تطوير الخلفية',
        'Data Analysis': '📊 تحليل البيانات',
        'Marketing': '📈 التسويق',
        'Web Developer': '🌐 تطوير المواقع',
        'Data Engineering': '🔧 هندسة البيانات'
    }
    
    # Countries in Arabic
    COUNTRIES_AR = {
        'Egypt': '🇪🇬 مصر',
        'Saudi Arabia': '🇸🇦 السعودية',
        'UAE': '🇦🇪 الإمارات',
        'Remote': '🌐 عن بُعد'
    }
    
    # Job types in Arabic
    JOB_TYPES_AR = {
        'Full-time': '⏰ دوام كامل',
        'Part-time': '🕐 دوام جزئي',
        'Internship': '🎓 تدريب',
        'Remote': '🏠 عن بُعد'
    }
    
    # CV operations
    CV_OPERATIONS_START = "📄 **مساعد السيرة الذاتية**\n\nماذا تريد أن تفعل بسيرتك الذاتية؟"
    CV_CREATE = "✨ **إنشاء سيرة ذاتية جديدة**\n\nسأساعدك في إنشاء سيرة ذاتية احترافية خطوة بخطوة.\nلنبدأ بمعلوماتك الشخصية.\n\nيرجى كتابة اسمك الكامل:"
    CV_ANALYZE = "🔍 **تحليل السيرة الذاتية**\n\nيرجى رفع ملف سيرتك الذاتية (PDF أو Word) للتحليل."
    CV_EDIT = "✏️ **تعديل السيرة الذاتية**\n\nيرجى رفع ملف سيرتك الذاتية الحالي الذي تريد تعديله."
    
    CV_OPERATIONS_BUTTONS = {
        'create_cv': '✨ إنشاء سيرة ذاتية جديدة',
        'analyze_cv': '🔍 تحليل السيرة الذاتية',
        'edit_cv': '✏️ تعديل السيرة الذاتية'
    }
    
    # Job matching
    JOB_MATCHING_START = """🎯 **مطابقة الوظائف مع السيرة الذاتية**

ارفع سيرتك الذاتية للعثور على الوظائف المناسبة لملفك الشخصي.
سأقوم بتحليل سيرتك الذاتية وإظهار درجات التوافق مع الوظائف المتاحة."""
    
    # Admin panel
    ADMIN_PANEL_START = "🔧 **لوحة الإدارة**\n\nاختر خياراً:"
    ADMIN_BUTTONS = {
        'admin_users': '👥 عرض المستخدمين',
        'admin_search': '🔍 البحث عن مستخدم',
        'admin_stats': '📊 الإحصائيات',
        'admin_bans': '🚫 إدارة الحظر'
    }
    
    # Help messages
    HELP_MESSAGE = """
🆘 **المساعدة - كيفية استخدام بوت الوظائف**

**الميزات الرئيسية:**
🔍 **البحث عن الوظائف** - ابحث عن وظائف حسب الفئة والموقع والنوع
📄 **مساعد السيرة الذاتية** - إنشاء أو تحليل أو تعديل سيرتك الذاتية
🎯 **مطابقة الوظائف** - اعثر على الوظائف المناسبة لسيرتك الذاتية

**الأوامر:**
/start - بدء البوت وإظهار القائمة الرئيسية
/help - إظهار رسالة المساعدة هذه
/admin - لوحة الإدارة (للمشرفين فقط)

**كيفية الاستخدام:**
1. استخدم الأزرار للتنقل بين الخيارات
2. ارفع سيرتك الذاتية للتحليل أو مطابقة الوظائف
3. اتبع التعليمات للبحث عن الوظائف
4. صدّر النتائج إلى Excel أو PDF

**نصائح:**
• تأكد من أن سيرتك الذاتية بصيغة PDF أو Word
• كن محدداً عند البحث عن الوظائف
• تحقق من حدود الاستخدام اليومية

تحتاج مساعدة أكثر؟ تواصل مع الدعم!
    """
    
    # Error messages
    ERROR_MESSAGES = {
        'general_error': 'عذراً، حدث خطأ ما. يرجى المحاولة مرة أخرى.',
        'file_too_large': '❌ **الملف كبير جداً**\n\nيرجى رفع ملف أصغر من 10 ميجابايت.',
        'invalid_file_type': '❌ **نوع ملف غير صالح**\n\nيرجى رفع مستند PDF أو Word (.pdf, .doc, .docx)',
        'usage_limit_exceeded': '❌ **تم تجاوز حد الاستخدام**\n\n{reason}\n\nقم بترقية خطتك للمزيد من الاستخدام!',
        'user_not_found': '❌ المستخدم غير موجود. يرجى استخدام /start أولاً.',
        'access_denied': '❌ تم رفض الوصول.',
        'no_jobs_found': '😔 **لم يتم العثور على وظائف**\n\nعذراً، لم أتمكن من العثور على أي وظائف تطابق معاييرك. جرب معايير بحث مختلفة.',
        'cv_processing_error': '❌ **خطأ في معالجة السيرة الذاتية**\n\nلم أتمكن من استخراج النص من سيرتك الذاتية. يرجى التأكد من أنها مستند PDF أو Word صالح.'
    }
    
    # Success messages
    SUCCESS_MESSAGES = {
        'cv_uploaded': '📄 تتم معالجة سيرتك الذاتية... يرجى الانتظار.',
        'cv_analysis_start': '🤖 أقوم بتحليل سيرتك الذاتية بالذكاء الاصطناعي... يرجى الانتظار.',
        'cv_generated': '✅ **سيرتك الذاتية جاهزة!**\n\nإليك سيرتك الذاتية المنسقة بشكل احترافي.',
        'search_started': '🔍 **البحث عن الوظائف...**\n\nيرجى الانتظار بينما أبحث عن الوظائف...'
    }
    
    # CV creation steps
    CV_CREATION_STEPS = {
        'email': '📧 **عنوان البريد الإلكتروني**\n\nيرجى كتابة عنوان بريدك الإلكتروني:',
        'experience': '💼 **الخبرة العملية**\n\nيرجى وصف خبرتك العملية (المسمى الوظيفي، الشركة، المدة، المسؤوليات):',
        'education': '🎓 **التعليم**\n\nيرجى كتابة خلفيتك التعليمية (الدرجة، المؤسسة، السنة):',
        'skills': '🛠️ **المهارات**\n\nيرجى سرد مهاراتك (مفصولة بفواصل):',
        'generating': '📄 أقوم بإنشاء سيرتك الذاتية... يرجى الانتظار.'
    }
    
    # Statistics labels
    STATS_LABELS = {
        'total_users': 'إجمالي المستخدمين',
        'active_users': 'المستخدمون النشطون',
        'banned_users': 'المستخدمون المحظورون',
        'new_users_today': 'مستخدمون جدد اليوم',
        'active_today': 'نشطون اليوم',
        'total_actions': 'إجمالي العمليات',
        'unique_users': 'مستخدمون فريدون'
    }
    
    # Navigation buttons
    NAVIGATION_BUTTONS = {
        'back_to_main': '🔙 العودة للقائمة الرئيسية',
        'back': '🔙 رجوع',
        'export_excel': '📊 تصدير إلى Excel',
        'export_pdf': '📄 تصدير إلى PDF',
        'view_details': '👁️ عرض التفاصيل',
        'apply_link': '🔗 رابط التقديم',
        'save_job': '💾 حفظ الوظيفة'
    }
    
    # Job search results
    JOB_RESULTS_HEADER = "✅ **تم العثور على {count} وظيفة**\n\n"
    JOB_RESULTS_FOOTER = "استخدم الأزرار أدناه لعرض التفاصيل أو تصدير النتائج."
    JOB_MORE_RESULTS = "... و {count} وظيفة أخرى\n\n"
    
    # CV analysis results
    CV_ANALYSIS_HEADER = "🔍 **نتائج تحليل السيرة الذاتية**\n\n"
    CV_ANALYSIS_SCORE = "**النتيجة الإجمالية:** {score}/100\n\n"
    CV_ANALYSIS_STRENGTHS = "**✅ نقاط القوة:**\n"
    CV_ANALYSIS_WEAKNESSES = "**⚠️ مجالات التحسين:**\n"
    CV_ANALYSIS_RECOMMENDATIONS = "**💡 التوصيات:**\n"
    CV_ANALYSIS_SUMMARY = "**📋 الملخص:**\n{summary}"
