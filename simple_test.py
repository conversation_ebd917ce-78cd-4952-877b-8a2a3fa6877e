#!/usr/bin/env python3
"""
Simple test to check imports
"""

print("🧪 Testing imports...")

try:
    import sys
    print(f"✅ Python version: {sys.version}")
    print(f"✅ Python path: {sys.executable}")
    
    import telegram
    print("✅ telegram module imported successfully")
    
    from telegram.ext import Application
    print("✅ telegram.ext.Application imported successfully")
    
    import pandas
    print("✅ pandas imported successfully")
    
    import openai
    print("✅ openai imported successfully")
    
    print("\n🎉 All basic imports successful!")
    print("\n📝 Your bot should work now!")
    
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("\n💡 Try installing the missing package:")
    print("pip install python-telegram-bot pandas openai")
    
except Exception as e:
    print(f"❌ Unexpected error: {e}")

print("\n🔧 To run the bot:")
print("1. Make sure all API keys are in .env file")
print("2. Run: python main.py")
