"""
Export utilities for job data and reports
"""
import pandas as pd
import os
from datetime import datetime
from typing import List, Dict, Any
from utils.logger import logger

class JobExporter:
    """Export job search results to various formats"""
    
    def __init__(self):
        self.exports_dir = "exports"
        os.makedirs(self.exports_dir, exist_ok=True)
    
    def export_jobs_to_excel(self, jobs: List[Dict[str, Any]], user_id: int) -> str:
        """Export job results to Excel file"""
        try:
            if not jobs:
                logger.warning("No jobs to export")
                return ""
            
            # Prepare data for DataFrame
            df_data = []
            for job in jobs:
                df_data.append({
                    'Job Title': job.get('title', 'N/A'),
                    'Company': job.get('company', 'N/A'),
                    'Location': job.get('location', 'N/A'),
                    'Posted Date': job.get('posted_date', 'N/A'),
                    'Job Level': job.get('job_level', 'N/A'),
                    'Application Link': job.get('link', 'N/A'),
                    'Compatibility Score': job.get('compatibility_score', 'N/A'),
                    'Scraped At': job.get('scraped_at', 'N/A')
                })
            
            df = pd.DataFrame(df_data)
            
            # Generate filename
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"job_search_results_{user_id}_{timestamp}.xlsx"
            filepath = os.path.join(self.exports_dir, filename)
            
            # Export to Excel with formatting
            with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='Job Results', index=False)
                
                # Get workbook and worksheet
                workbook = writer.book
                worksheet = writer.sheets['Job Results']
                
                # Auto-adjust column widths
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 50)
                    worksheet.column_dimensions[column_letter].width = adjusted_width
            
            logger.info(f"Jobs exported to Excel: {filepath}")
            return filepath
            
        except Exception as e:
            logger.error(f"Error exporting jobs to Excel: {e}")
            return ""
    
    def export_jobs_to_csv(self, jobs: List[Dict[str, Any]], user_id: int) -> str:
        """Export job results to CSV file"""
        try:
            if not jobs:
                return ""
            
            # Prepare data for DataFrame
            df_data = []
            for job in jobs:
                df_data.append({
                    'Job Title': job.get('title', 'N/A'),
                    'Company': job.get('company', 'N/A'),
                    'Location': job.get('location', 'N/A'),
                    'Posted Date': job.get('posted_date', 'N/A'),
                    'Job Level': job.get('job_level', 'N/A'),
                    'Application Link': job.get('link', 'N/A'),
                    'Compatibility Score': job.get('compatibility_score', 'N/A'),
                    'Scraped At': job.get('scraped_at', 'N/A')
                })
            
            df = pd.DataFrame(df_data)
            
            # Generate filename
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"job_search_results_{user_id}_{timestamp}.csv"
            filepath = os.path.join(self.exports_dir, filename)
            
            # Export to CSV
            df.to_csv(filepath, index=False, encoding='utf-8')
            
            logger.info(f"Jobs exported to CSV: {filepath}")
            return filepath
            
        except Exception as e:
            logger.error(f"Error exporting jobs to CSV: {e}")
            return ""
    
    def create_job_summary_report(self, jobs: List[Dict[str, Any]], search_params: Dict[str, str], user_id: int) -> str:
        """Create a comprehensive job search summary report"""
        try:
            if not jobs:
                return ""
            
            # Generate filename
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"job_search_summary_{user_id}_{timestamp}.xlsx"
            filepath = os.path.join(self.exports_dir, filename)
            
            with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
                # Main job results
                df_jobs = pd.DataFrame([{
                    'Job Title': job.get('title', 'N/A'),
                    'Company': job.get('company', 'N/A'),
                    'Location': job.get('location', 'N/A'),
                    'Posted Date': job.get('posted_date', 'N/A'),
                    'Job Level': job.get('job_level', 'N/A'),
                    'Application Link': job.get('link', 'N/A'),
                    'Compatibility Score': job.get('compatibility_score', 'N/A')
                } for job in jobs])
                
                df_jobs.to_excel(writer, sheet_name='Job Results', index=False)
                
                # Search summary
                summary_data = {
                    'Search Parameter': ['Category', 'Country', 'Job Type', 'Total Results', 'Search Date'],
                    'Value': [
                        search_params.get('category', 'N/A'),
                        search_params.get('country', 'N/A'),
                        search_params.get('job_type', 'N/A'),
                        len(jobs),
                        datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    ]
                }
                
                df_summary = pd.DataFrame(summary_data)
                df_summary.to_excel(writer, sheet_name='Search Summary', index=False)
                
                # Company analysis
                if jobs:
                    companies = [job.get('company', 'Unknown') for job in jobs]
                    company_counts = pd.Series(companies).value_counts()
                    
                    df_companies = pd.DataFrame({
                        'Company': company_counts.index,
                        'Job Count': company_counts.values
                    })
                    
                    df_companies.to_excel(writer, sheet_name='Company Analysis', index=False)
                
                # Location analysis
                if jobs:
                    locations = [job.get('location', 'Unknown') for job in jobs]
                    location_counts = pd.Series(locations).value_counts()
                    
                    df_locations = pd.DataFrame({
                        'Location': location_counts.index,
                        'Job Count': location_counts.values
                    })
                    
                    df_locations.to_excel(writer, sheet_name='Location Analysis', index=False)
            
            logger.info(f"Job summary report created: {filepath}")
            return filepath
            
        except Exception as e:
            logger.error(f"Error creating job summary report: {e}")
            return ""
    
    def cleanup_old_exports(self, days_old: int = 7):
        """Clean up old export files"""
        try:
            cutoff_time = datetime.now() - timedelta(days=days_old)
            
            for filename in os.listdir(self.exports_dir):
                filepath = os.path.join(self.exports_dir, filename)
                
                if os.path.isfile(filepath):
                    file_time = datetime.fromtimestamp(os.path.getctime(filepath))
                    
                    if file_time < cutoff_time:
                        os.remove(filepath)
                        logger.info(f"Cleaned up old export file: {filename}")
                        
        except Exception as e:
            logger.error(f"Error cleaning up old exports: {e}")

class CVExporter:
    """Export CV analysis and reports"""
    
    def __init__(self):
        self.exports_dir = "exports"
        os.makedirs(self.exports_dir, exist_ok=True)
    
    def export_cv_analysis_report(self, analysis: Dict[str, Any], user_id: int) -> str:
        """Export CV analysis to Excel report"""
        try:
            # Generate filename
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"cv_analysis_report_{user_id}_{timestamp}.xlsx"
            filepath = os.path.join(self.exports_dir, filename)
            
            with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
                # Analysis summary
                summary_data = {
                    'Metric': ['Overall Score', 'Analysis Date'],
                    'Value': [
                        analysis.get('overall_score', 'N/A'),
                        datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    ]
                }
                
                df_summary = pd.DataFrame(summary_data)
                df_summary.to_excel(writer, sheet_name='Summary', index=False)
                
                # Strengths
                strengths = analysis.get('strengths', [])
                if strengths:
                    df_strengths = pd.DataFrame({'Strengths': strengths})
                    df_strengths.to_excel(writer, sheet_name='Strengths', index=False)
                
                # Weaknesses
                weaknesses = analysis.get('weaknesses', [])
                if weaknesses:
                    df_weaknesses = pd.DataFrame({'Areas for Improvement': weaknesses})
                    df_weaknesses.to_excel(writer, sheet_name='Improvements', index=False)
                
                # Recommendations
                recommendations = analysis.get('recommendations', [])
                if recommendations:
                    df_recommendations = pd.DataFrame({'Recommendations': recommendations})
                    df_recommendations.to_excel(writer, sheet_name='Recommendations', index=False)
                
                # Skills assessment
                skills_assessment = analysis.get('skills_assessment', {})
                if skills_assessment:
                    skills_data = []
                    
                    for category, skills in skills_assessment.items():
                        if isinstance(skills, list):
                            for skill in skills:
                                skills_data.append({'Category': category, 'Skill': skill})
                    
                    if skills_data:
                        df_skills = pd.DataFrame(skills_data)
                        df_skills.to_excel(writer, sheet_name='Skills Assessment', index=False)
            
            logger.info(f"CV analysis report exported: {filepath}")
            return filepath
            
        except Exception as e:
            logger.error(f"Error exporting CV analysis report: {e}")
            return ""
