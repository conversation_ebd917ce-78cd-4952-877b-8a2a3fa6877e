"""
Inline keyboards for the Telegram bot
"""
from telegram import InlineKeyboardButton, InlineKeyboardMarkup
from config.settings import Config

class BotKeyboards:
    """Class containing all bot keyboards"""
    
    @staticmethod
    def main_menu() -> InlineKeyboardMarkup:
        """Main menu keyboard"""
        keyboard = [
            [InlineKeyboardButton("🔍 Job Search", callback_data="job_search")],
            [InlineKeyboardButton("📄 CV Assistant", callback_data="cv_operations")],
            [InlineKeyboardButton("🎯 Job-CV Matching", callback_data="job_matching")],
            [InlineKeyboardButton("ℹ️ Help", callback_data="help")],
            [InlineKeyboardButton("⚙️ Admin Panel", callback_data="admin_panel")]
        ]
        return InlineKeyboardMarkup(keyboard)
    
    @staticmethod
    def job_categories() -> InlineKeyboardMarkup:
        """Job categories keyboard"""
        keyboard = []
        for category in Config.JOB_CATEGORIES:
            keyboard.append([InlineKeyboardButton(
                f"💼 {category}", 
                callback_data=f"category_{category.lower().replace(' ', '_')}"
            )])
        keyboard.append([InlineKeyboardButton("🔙 Back to Main Menu", callback_data="main_menu")])
        return InlineKeyboardMarkup(keyboard)
    
    @staticmethod
    def countries() -> InlineKeyboardMarkup:
        """Countries keyboard"""
        keyboard = []
        country_emojis = {"Egypt": "🇪🇬", "Saudi Arabia": "🇸🇦", "UAE": "🇦🇪", "Remote": "🌐"}
        
        for country in Config.COUNTRIES:
            emoji = country_emojis.get(country, "🌍")
            keyboard.append([InlineKeyboardButton(
                f"{emoji} {country}", 
                callback_data=f"country_{country.lower().replace(' ', '_')}"
            )])
        keyboard.append([InlineKeyboardButton("🔙 Back", callback_data="job_search")])
        return InlineKeyboardMarkup(keyboard)
    
    @staticmethod
    def job_types() -> InlineKeyboardMarkup:
        """Job types keyboard"""
        keyboard = []
        type_emojis = {"Full-time": "⏰", "Part-time": "🕐", "Internship": "🎓", "Remote": "🏠"}
        
        for job_type in Config.JOB_TYPES:
            emoji = type_emojis.get(job_type, "💼")
            keyboard.append([InlineKeyboardButton(
                f"{emoji} {job_type}", 
                callback_data=f"jobtype_{job_type.lower().replace('-', '_')}"
            )])
        keyboard.append([InlineKeyboardButton("🔙 Back", callback_data="select_country")])
        return InlineKeyboardMarkup(keyboard)
    
    @staticmethod
    def cv_operations() -> InlineKeyboardMarkup:
        """CV operations keyboard"""
        keyboard = [
            [InlineKeyboardButton("✨ Create New CV", callback_data="create_cv")],
            [InlineKeyboardButton("🔍 Analyze CV", callback_data="analyze_cv")],
            [InlineKeyboardButton("✏️ Edit CV", callback_data="edit_cv")],
            [InlineKeyboardButton("🔙 Back to Main Menu", callback_data="main_menu")]
        ]
        return InlineKeyboardMarkup(keyboard)
    
    @staticmethod
    def admin_panel() -> InlineKeyboardMarkup:
        """Admin panel keyboard"""
        keyboard = [
            [InlineKeyboardButton("👥 View Users", callback_data="admin_users")],
            [InlineKeyboardButton("🔍 Search User", callback_data="admin_search")],
            [InlineKeyboardButton("📊 Statistics", callback_data="admin_stats")],
            [InlineKeyboardButton("🚫 Manage Bans", callback_data="admin_bans")],
            [InlineKeyboardButton("🔙 Back to Main Menu", callback_data="main_menu")]
        ]
        return InlineKeyboardMarkup(keyboard)
    
    @staticmethod
    def job_result_actions(job_id: str) -> InlineKeyboardMarkup:
        """Job result action buttons"""
        keyboard = [
            [InlineKeyboardButton("👁️ View Details", callback_data=f"view_job_{job_id}")],
            [InlineKeyboardButton("🔗 Apply Link", callback_data=f"apply_job_{job_id}")],
            [InlineKeyboardButton("💾 Save Job", callback_data=f"save_job_{job_id}")]
        ]
        return InlineKeyboardMarkup(keyboard)
    
    @staticmethod
    def export_options() -> InlineKeyboardMarkup:
        """Export options keyboard"""
        keyboard = [
            [InlineKeyboardButton("📊 Export to Excel", callback_data="export_excel")],
            [InlineKeyboardButton("📄 Export to PDF", callback_data="export_pdf")],
            [InlineKeyboardButton("🔙 Back", callback_data="view_results")]
        ]
        return InlineKeyboardMarkup(keyboard)
    
    @staticmethod
    def cv_format_selection() -> InlineKeyboardMarkup:
        """CV format selection keyboard"""
        keyboard = [
            [InlineKeyboardButton("📄 Word Document", callback_data="format_docx")],
            [InlineKeyboardButton("📋 PDF Document", callback_data="format_pdf")],
            [InlineKeyboardButton("📊 Both Formats", callback_data="format_both")]
        ]
        return InlineKeyboardMarkup(keyboard)
    
    @staticmethod
    def confirmation() -> InlineKeyboardMarkup:
        """Confirmation keyboard"""
        keyboard = [
            [InlineKeyboardButton("✅ Yes", callback_data="confirm_yes")],
            [InlineKeyboardButton("❌ No", callback_data="confirm_no")]
        ]
        return InlineKeyboardMarkup(keyboard)
    
    @staticmethod
    def back_to_main() -> InlineKeyboardMarkup:
        """Simple back to main menu keyboard"""
        keyboard = [[InlineKeyboardButton("🔙 Back to Main Menu", callback_data="main_menu")]]
        return InlineKeyboardMarkup(keyboard)
