"""
Inline keyboards for the Telegram bot
"""
from telegram import Inline<PERSON>eyboardButton, InlineKeyboardMarkup
from config.settings import Config
from config.messages_ar import ArabicMessages

class BotKeyboards:
    """Class containing all bot keyboards"""
    
    @staticmethod
    def main_menu() -> InlineKeyboardMarkup:
        """Main menu keyboard"""
        keyboard = [
            [InlineKeyboardButton(ArabicMessages.MAIN_MENU_BUTTONS['job_search'], callback_data="job_search")],
            [InlineKeyboardButton(ArabicMessages.MAIN_MENU_BUTTONS['cv_operations'], callback_data="cv_operations")],
            [InlineKeyboardButton(ArabicMessages.MAIN_MENU_BUTTONS['job_matching'], callback_data="job_matching")],
            [InlineKeyboardButton(ArabicMessages.MAIN_MENU_BUTTONS['help'], callback_data="help")],
            [InlineKeyboardButton(ArabicMessages.MAIN_MENU_BUTTONS['admin_panel'], callback_data="admin_panel")]
        ]
        return Inline<PERSON><PERSON>boardMarkup(keyboard)
    
    @staticmethod
    def job_categories() -> InlineKeyboardMarkup:
        """Job categories keyboard"""
        keyboard = []
        for category in Config.JOB_CATEGORIES:
            arabic_name = ArabicMessages.JOB_CATEGORIES_AR.get(category, f"💼 {category}")
            keyboard.append([InlineKeyboardButton(
                arabic_name,
                callback_data=f"category_{category.lower().replace(' ', '_')}"
            )])
        keyboard.append([InlineKeyboardButton(ArabicMessages.NAVIGATION_BUTTONS['back_to_main'], callback_data="main_menu")])
        return InlineKeyboardMarkup(keyboard)
    
    @staticmethod
    def countries() -> InlineKeyboardMarkup:
        """Countries keyboard"""
        keyboard = []

        for country in Config.COUNTRIES:
            arabic_name = ArabicMessages.COUNTRIES_AR.get(country, f"🌍 {country}")
            keyboard.append([InlineKeyboardButton(
                arabic_name,
                callback_data=f"country_{country.lower().replace(' ', '_')}"
            )])
        keyboard.append([InlineKeyboardButton(ArabicMessages.NAVIGATION_BUTTONS['back'], callback_data="job_search")])
        return InlineKeyboardMarkup(keyboard)
    
    @staticmethod
    def job_types() -> InlineKeyboardMarkup:
        """Job types keyboard"""
        keyboard = []

        for job_type in Config.JOB_TYPES:
            arabic_name = ArabicMessages.JOB_TYPES_AR.get(job_type, f"💼 {job_type}")
            keyboard.append([InlineKeyboardButton(
                arabic_name,
                callback_data=f"jobtype_{job_type.lower().replace('-', '_')}"
            )])
        keyboard.append([InlineKeyboardButton(ArabicMessages.NAVIGATION_BUTTONS['back'], callback_data="select_country")])
        return InlineKeyboardMarkup(keyboard)
    
    @staticmethod
    def cv_operations() -> InlineKeyboardMarkup:
        """CV operations keyboard"""
        keyboard = [
            [InlineKeyboardButton(ArabicMessages.CV_OPERATIONS_BUTTONS['create_cv'], callback_data="create_cv")],
            [InlineKeyboardButton(ArabicMessages.CV_OPERATIONS_BUTTONS['analyze_cv'], callback_data="analyze_cv")],
            [InlineKeyboardButton(ArabicMessages.CV_OPERATIONS_BUTTONS['edit_cv'], callback_data="edit_cv")],
            [InlineKeyboardButton(ArabicMessages.NAVIGATION_BUTTONS['back_to_main'], callback_data="main_menu")]
        ]
        return InlineKeyboardMarkup(keyboard)
    
    @staticmethod
    def admin_panel() -> InlineKeyboardMarkup:
        """Admin panel keyboard"""
        keyboard = [
            [InlineKeyboardButton(ArabicMessages.ADMIN_BUTTONS['admin_users'], callback_data="admin_users")],
            [InlineKeyboardButton(ArabicMessages.ADMIN_BUTTONS['admin_search'], callback_data="admin_search")],
            [InlineKeyboardButton(ArabicMessages.ADMIN_BUTTONS['admin_stats'], callback_data="admin_stats")],
            [InlineKeyboardButton(ArabicMessages.ADMIN_BUTTONS['admin_bans'], callback_data="admin_bans")],
            [InlineKeyboardButton(ArabicMessages.NAVIGATION_BUTTONS['back_to_main'], callback_data="main_menu")]
        ]
        return InlineKeyboardMarkup(keyboard)
    
    @staticmethod
    def job_result_actions(job_id: str) -> InlineKeyboardMarkup:
        """Job result action buttons"""
        keyboard = [
            [InlineKeyboardButton("👁️ View Details", callback_data=f"view_job_{job_id}")],
            [InlineKeyboardButton("🔗 Apply Link", callback_data=f"apply_job_{job_id}")],
            [InlineKeyboardButton("💾 Save Job", callback_data=f"save_job_{job_id}")]
        ]
        return InlineKeyboardMarkup(keyboard)
    
    @staticmethod
    def export_options() -> InlineKeyboardMarkup:
        """Export options keyboard"""
        keyboard = [
            [InlineKeyboardButton("📊 Export to Excel", callback_data="export_excel")],
            [InlineKeyboardButton("📄 Export to PDF", callback_data="export_pdf")],
            [InlineKeyboardButton("🔙 Back", callback_data="view_results")]
        ]
        return InlineKeyboardMarkup(keyboard)
    
    @staticmethod
    def cv_format_selection() -> InlineKeyboardMarkup:
        """CV format selection keyboard"""
        keyboard = [
            [InlineKeyboardButton("📄 Word Document", callback_data="format_docx")],
            [InlineKeyboardButton("📋 PDF Document", callback_data="format_pdf")],
            [InlineKeyboardButton("📊 Both Formats", callback_data="format_both")]
        ]
        return InlineKeyboardMarkup(keyboard)
    
    @staticmethod
    def confirmation() -> InlineKeyboardMarkup:
        """Confirmation keyboard"""
        keyboard = [
            [InlineKeyboardButton("✅ Yes", callback_data="confirm_yes")],
            [InlineKeyboardButton("❌ No", callback_data="confirm_no")]
        ]
        return InlineKeyboardMarkup(keyboard)
    
    @staticmethod
    def back_to_main() -> InlineKeyboardMarkup:
        """Simple back to main menu keyboard"""
        keyboard = [[InlineKeyboardButton("🔙 Back to Main Menu", callback_data="main_menu")]]
        return InlineKeyboardMarkup(keyboard)
