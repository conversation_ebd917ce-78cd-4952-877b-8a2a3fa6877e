"""
Admin Telegram IDs configuration
Add your Telegram ID here to access admin features
"""

# Admin Telegram IDs - Replace with actual admin IDs
ADMIN_IDS = [
    # Add your Telegram ID here
    # Example: 123456789,
    # You can get your Telegram ID by messaging @userinfobot
]

def is_admin(user_id: int) -> bool:
    """Check if user is admin"""
    return user_id in ADMIN_IDS

def add_admin(user_id: int) -> bool:
    """Add new admin (for development purposes)"""
    if user_id not in ADMIN_IDS:
        ADMIN_IDS.append(user_id)
        return True
    return False
