"""
Telegram bot handlers for job search and CV operations
"""
import os
import asyncio
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes
from telegram.constants import ParseMode
from typing import Dict, Any, Optional

from bot.keyboards import BotKeyboards
from bot.states import ConversationState, JobSearchState, CVState, AdminState
from config.admin_ids import is_admin
from config.settings import Config
from database.user_manager import UserManager
from services.job_scraper import JobScraper
from services.ai_analyzer import AIAnalyzer
from services.cv_processor import CVProcessor
from services.job_matcher import JobMatcher
from admin.dashboard import AdminDashboard
from utils.logger import logger
from utils.validators import validate_file_extension

class BotHandlers:
    """Main bot handlers class"""
    
    def __init__(self, user_manager: UserManager):
        self.user_manager = user_manager
        self.job_scraper = JobScraper()
        self.ai_analyzer = AIAnalyzer()
        self.cv_processor = CVProcessor()
        self.job_matcher = JobMatcher(self.ai_analyzer)
        self.admin_dashboard = AdminDashboard(user_manager)
        
        # Store user states and data
        self.user_states: Dict[int, str] = {}
        self.user_data: Dict[int, Dict[str, Any]] = {}
    
    async def start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /start command"""
        try:
            user = update.effective_user
            chat_id = update.effective_chat.id
            
            # Register user
            self.user_manager.register_user(
                telegram_id=user.id,
                username=user.username,
                first_name=user.first_name,
                last_name=user.last_name
            )
            
            # Log usage
            self.user_manager.log_usage(user.id, 'start_command', 'User started bot')
            
            welcome_message = f"""
🤖 **Welcome to the Intelligent Job Search Bot!** 

Hello {user.first_name}! I'm here to help you with:

🔍 **Job Search** - Find jobs across multiple platforms
📄 **CV Assistant** - Create, analyze, and edit your CV
🎯 **Job Matching** - Match your CV with relevant jobs
📊 **AI Analysis** - Get AI-powered insights

Choose an option below to get started:
            """
            
            await update.message.reply_text(
                welcome_message,
                reply_markup=BotKeyboards.main_menu(),
                parse_mode=ParseMode.MARKDOWN
            )
            
            # Set user state
            self.user_states[user.id] = ConversationState.MAIN_MENU.value
            
        except Exception as e:
            logger.error(f"Error in start command: {e}")
            await update.message.reply_text("Sorry, something went wrong. Please try again.")
    
    async def help_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /help command"""
        help_text = """
🆘 **Help - How to Use the Job Bot**

**Main Features:**
🔍 **Job Search** - Search for jobs by category, location, and type
📄 **CV Assistant** - Create, analyze, or edit your CV
🎯 **Job Matching** - Find jobs that match your CV

**Commands:**
/start - Start the bot and show main menu
/help - Show this help message
/admin - Admin panel (admin only)

**How to Use:**
1. Use the buttons to navigate through options
2. Upload your CV for analysis or job matching
3. Follow the prompts to search for jobs
4. Export results to Excel or PDF

**Tips:**
• Make sure your CV is in PDF or Word format
• Be specific when searching for jobs
• Check your daily usage limits

Need more help? Contact support!
        """
        
        await update.message.reply_text(
            help_text,
            reply_markup=BotKeyboards.back_to_main(),
            parse_mode=ParseMode.MARKDOWN
        )
    
    async def admin_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /admin command"""
        user = update.effective_user
        
        if not is_admin(user.id):
            await update.message.reply_text("❌ You don't have admin privileges.")
            return
        
        await update.message.reply_text(
            "🔧 **Admin Panel**\n\nSelect an option:",
            reply_markup=BotKeyboards.admin_panel(),
            parse_mode=ParseMode.MARKDOWN
        )
        
        self.user_states[user.id] = AdminState.MAIN_ADMIN_MENU.value
    
    async def stats_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /stats command"""
        user = update.effective_user
        
        # Get user statistics
        user_info = self.user_manager.get_user(user.id)
        if not user_info:
            await update.message.reply_text("❌ User not found. Please use /start first.")
            return
        
        usage_check = self.user_manager.check_usage_limit(user.id)
        daily_usage = self.user_manager.get_daily_usage(user.id)
        
        stats_text = f"""
📊 **Your Statistics**

👤 **User Info:**
• Plan: {user_info.get('current_plan', 'free').title()}
• Total Usage: {user_info.get('usage_count', 0)}
• Registration: {user_info.get('registration_date', 'Unknown')}

📈 **Today's Usage:**
• Used: {daily_usage}
• Limit: {usage_check.get('limit', 'Unknown')}
• Remaining: {max(0, usage_check.get('limit', 0) - daily_usage) if usage_check.get('limit') != 'unlimited' else 'Unlimited'}

✅ **Status:** {'Active' if user_info.get('is_active', True) else 'Inactive'}
        """
        
        await update.message.reply_text(
            stats_text,
            reply_markup=BotKeyboards.back_to_main(),
            parse_mode=ParseMode.MARKDOWN
        )
    
    async def handle_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle callback queries from inline keyboards"""
        try:
            query = update.callback_query
            await query.answer()
            
            user_id = query.from_user.id
            data = query.data
            
            # Check usage limits for non-admin actions
            if not is_admin(user_id) and not data.startswith(('main_menu', 'help', 'admin')):
                usage_check = self.user_manager.check_usage_limit(user_id)
                if not usage_check['allowed']:
                    await query.edit_message_text(
                        f"❌ **Usage Limit Exceeded**\n\n{usage_check['reason']}\n\nUpgrade your plan for more usage!",
                        reply_markup=BotKeyboards.back_to_main(),
                        parse_mode=ParseMode.MARKDOWN
                    )
                    return
            
            # Route callback to appropriate handler
            if data == "main_menu":
                await self._handle_main_menu(query)
            elif data == "job_search":
                await self._handle_job_search_start(query)
            elif data.startswith("category_"):
                await self._handle_category_selection(query, data)
            elif data.startswith("country_"):
                await self._handle_country_selection(query, data)
            elif data.startswith("jobtype_"):
                await self._handle_job_type_selection(query, data)
            elif data == "cv_operations":
                await self._handle_cv_operations(query)
            elif data.startswith("cv_"):
                await self._handle_cv_action(query, data)
            elif data == "job_matching":
                await self._handle_job_matching_start(query)
            elif data.startswith("admin_"):
                await self._handle_admin_action(query, data)
            elif data == "help":
                await self._handle_help_callback(query)
            else:
                await query.edit_message_text("Unknown action. Please try again.")
            
        except Exception as e:
            logger.error(f"Error handling callback: {e}")
            await query.edit_message_text("Sorry, something went wrong. Please try again.")
    
    async def _handle_main_menu(self, query):
        """Handle main menu callback"""
        await query.edit_message_text(
            "🏠 **Main Menu**\n\nChoose what you'd like to do:",
            reply_markup=BotKeyboards.main_menu(),
            parse_mode=ParseMode.MARKDOWN
        )
        self.user_states[query.from_user.id] = ConversationState.MAIN_MENU.value
    
    async def _handle_job_search_start(self, query):
        """Handle job search start"""
        await query.edit_message_text(
            "🔍 **Job Search**\n\nSelect a job category:",
            reply_markup=BotKeyboards.job_categories(),
            parse_mode=ParseMode.MARKDOWN
        )
        self.user_states[query.from_user.id] = JobSearchState.SELECTING_CATEGORY.value
    
    async def _handle_category_selection(self, query, data):
        """Handle job category selection"""
        category = data.replace("category_", "").replace("_", " ").title()
        
        # Store selected category
        user_id = query.from_user.id
        if user_id not in self.user_data:
            self.user_data[user_id] = {}
        self.user_data[user_id]['selected_category'] = category
        
        await query.edit_message_text(
            f"📍 **Selected Category:** {category}\n\nNow select a country:",
            reply_markup=BotKeyboards.countries(),
            parse_mode=ParseMode.MARKDOWN
        )
        self.user_states[user_id] = JobSearchState.SELECTING_COUNTRY.value
    
    async def _handle_country_selection(self, query, data):
        """Handle country selection"""
        country = data.replace("country_", "").replace("_", " ").title()
        
        # Store selected country
        user_id = query.from_user.id
        self.user_data[user_id]['selected_country'] = country
        
        category = self.user_data[user_id].get('selected_category', 'Unknown')
        
        await query.edit_message_text(
            f"📍 **Selected:** {category} in {country}\n\nSelect job type:",
            reply_markup=BotKeyboards.job_types(),
            parse_mode=ParseMode.MARKDOWN
        )
        self.user_states[user_id] = JobSearchState.SELECTING_JOB_TYPE.value
    
    async def _handle_job_type_selection(self, query, data):
        """Handle job type selection and start search"""
        job_type = data.replace("jobtype_", "").replace("_", " ").title()
        
        # Store selected job type
        user_id = query.from_user.id
        self.user_data[user_id]['selected_job_type'] = job_type
        
        # Get search parameters
        category = self.user_data[user_id].get('selected_category')
        country = self.user_data[user_id].get('selected_country')
        
        # Show searching message
        await query.edit_message_text(
            f"🔍 **Searching Jobs...**\n\n"
            f"Category: {category}\n"
            f"Location: {country}\n"
            f"Type: {job_type}\n\n"
            f"Please wait while I search for jobs...",
            parse_mode=ParseMode.MARKDOWN
        )
        
        # Start job search
        await self._perform_job_search(query, category, country, job_type)
    
    async def _perform_job_search(self, query, category: str, country: str, job_type: str):
        """Perform the actual job search"""
        try:
            user_id = query.from_user.id
            
            # Log the search
            self.user_manager.log_usage(
                user_id, 
                'job_search', 
                f'{category} in {country} ({job_type})'
            )
            self.user_manager.increment_usage(user_id)
            
            # Search for jobs
            jobs = self.job_scraper.search_jobs(category, country, job_type)
            
            if not jobs:
                await query.edit_message_text(
                    "😔 **No Jobs Found**\n\n"
                    "Sorry, I couldn't find any jobs matching your criteria. "
                    "Try different search parameters.",
                    reply_markup=BotKeyboards.main_menu(),
                    parse_mode=ParseMode.MARKDOWN
                )
                return
            
            # Store jobs for user
            self.user_data[user_id]['search_results'] = jobs
            
            # Show results
            await self._show_job_results(query, jobs)
            
        except Exception as e:
            logger.error(f"Error performing job search: {e}")
            await query.edit_message_text(
                "❌ **Search Error**\n\n"
                "Sorry, there was an error searching for jobs. Please try again later.",
                reply_markup=BotKeyboards.main_menu(),
                parse_mode=ParseMode.MARKDOWN
            )
    
    async def _show_job_results(self, query, jobs):
        """Show job search results"""
        try:
            results_text = f"✅ **Found {len(jobs)} Jobs**\n\n"
            
            for i, job in enumerate(jobs[:5], 1):  # Show first 5 jobs
                results_text += f"**{i}. {job.get('title', 'Unknown Title')}**\n"
                results_text += f"🏢 {job.get('company', 'Unknown Company')}\n"
                results_text += f"📍 {job.get('location', 'Unknown Location')}\n"
                results_text += f"📅 {job.get('posted_date', 'Recently')}\n\n"
            
            if len(jobs) > 5:
                results_text += f"... and {len(jobs) - 5} more jobs\n\n"
            
            results_text += "Use the buttons below to view details or export results."
            
            # Create action buttons
            keyboard = [
                [InlineKeyboardButton("📊 Export to Excel", callback_data="export_excel")],
                [InlineKeyboardButton("🔍 View All Details", callback_data="view_all_jobs")],
                [InlineKeyboardButton("🔙 New Search", callback_data="job_search")],
                [InlineKeyboardButton("🏠 Main Menu", callback_data="main_menu")]
            ]
            
            await query.edit_message_text(
                results_text,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode=ParseMode.MARKDOWN
            )
            
        except Exception as e:
            logger.error(f"Error showing job results: {e}")
            await query.edit_message_text("Error displaying results.")
    
    async def handle_document(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle document uploads (CV files)"""
        try:
            user_id = update.effective_user.id
            document = update.message.document
            
            # Validate file type
            if not validate_file_extension(document.file_name, ['pdf', 'doc', 'docx']):
                await update.message.reply_text(
                    "❌ **Invalid File Type**\n\n"
                    "Please upload a PDF or Word document (.pdf, .doc, .docx)",
                    reply_markup=BotKeyboards.back_to_main()
                )
                return
            
            # Check file size (10MB limit)
            if document.file_size > 10 * 1024 * 1024:
                await update.message.reply_text(
                    "❌ **File Too Large**\n\n"
                    "Please upload a file smaller than 10MB.",
                    reply_markup=BotKeyboards.back_to_main()
                )
                return
            
            # Download and process file
            await update.message.reply_text("📄 Processing your CV... Please wait.")
            
            # Download file
            file = await document.get_file()
            file_path = f"temp/{user_id}_{document.file_name}"
            await file.download_to_drive(file_path)
            
            # Process based on current state
            current_state = self.user_states.get(user_id)
            
            if current_state == CVState.UPLOADING_CV_FOR_ANALYSIS.value:
                await self._process_cv_analysis(update, file_path)
            elif current_state == CVState.UPLOADING_CV_FOR_EDIT.value:
                await self._process_cv_editing(update, file_path)
            else:
                # Default: CV analysis
                await self._process_cv_analysis(update, file_path)
            
            # Clean up file
            if os.path.exists(file_path):
                os.remove(file_path)
                
        except Exception as e:
            logger.error(f"Error handling document: {e}")
            await update.message.reply_text("Sorry, there was an error processing your file.")
    
    async def handle_text(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle text messages"""
        # This will be used for collecting user input during CV creation
        user_id = update.effective_user.id
        current_state = self.user_states.get(user_id)
        
        if current_state in [CVState.COLLECTING_PERSONAL_INFO.value, 
                           CVState.COLLECTING_EXPERIENCE.value,
                           CVState.COLLECTING_EDUCATION.value,
                           CVState.COLLECTING_SKILLS.value]:
            await self._handle_cv_creation_input(update, current_state)
        else:
            await update.message.reply_text(
                "I'm not sure what you mean. Please use the menu buttons.",
                reply_markup=BotKeyboards.main_menu()
            )
    
    async def error_handler(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle errors"""
        logger.error(f"Update {update} caused error {context.error}")
        
        if update and update.effective_message:
            await update.effective_message.reply_text(
                "Sorry, something went wrong. Please try again or use /start to restart."
            )
