"""
Configuration settings for the Telegram Job Bot
"""
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class Config:
    """Main configuration class"""
    
    # Bot Configuration
    TELEGRAM_BOT_TOKEN = os.getenv('TELEGRAM_BOT_TOKEN')
    
    # AI API Keys
    OPENAI_API_KEY = os.getenv('OPENAI_API_KEY')
    GEMINI_API_KEY = os.getenv('GEMINI_API_KEY')
    
    # Database Configuration
    USERS_DB_PATH = 'data/users.xlsx'
    USAGE_LOGS_DB_PATH = 'data/usage_logs.xlsx'
    
    # Job Search Configuration
    JOB_CATEGORIES = [
        'Front End',
        'Back End',
        'Data Analysis',
        'Marketing',
        'Web Developer',
        'Data Engineering'
    ]
    
    COUNTRIES = [
        'Egypt',
        'Saudi Arabia',
        'UAE',
        'Remote'
    ]
    
    JOB_TYPES = [
        'Full-time',
        'Part-time',
        'Internship',
        'Remote'
    ]
    
    # Usage Limits
    USAGE_LIMITS = {
        'free': 3,
        'pro': 20,
        'premium': float('inf')
    }
    
    # File Paths
    TEMP_DIR = 'temp'
    EXPORTS_DIR = 'exports'
    
    # Scraping Configuration
    SCRAPING_DELAY = 2  # seconds between requests
    MAX_JOBS_PER_SEARCH = 50
    
    # CV Configuration
    CV_TEMPLATES_DIR = 'templates'
    
    @classmethod
    def validate_config(cls):
        """Validate required configuration"""
        required_vars = [
            'TELEGRAM_BOT_TOKEN',
            'OPENAI_API_KEY',
            'GEMINI_API_KEY'
        ]
        
        missing_vars = []
        for var in required_vars:
            if not getattr(cls, var):
                missing_vars.append(var)
        
        if missing_vars:
            raise ValueError(f"Missing required environment variables: {', '.join(missing_vars)}")
        
        return True

# Create necessary directories
os.makedirs(Config.TEMP_DIR, exist_ok=True)
os.makedirs(Config.EXPORTS_DIR, exist_ok=True)
os.makedirs('data', exist_ok=True)
