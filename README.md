# 🤖 Telegram Job Search Bot

A comprehensive Telegram bot for intelligent job searching and CV analysis using AI.

## 🌟 Features

### 🔍 Job Search
- Search jobs by category (Front End, Back End, Data Analysis, Marketing, Web Developer, Data Engineering)
- Filter by country (Egypt, Saudi Arabia, UAE, Remote)
- Filter by job type (Full-time, Part-time, Internship, Remote)
- Export results to Excel/PDF

### 📄 CV Assistant
- **Create CV**: Interactive CV creation with professional templates
- **Analyze CV**: AI-powered CV analysis with strengths, weaknesses, and recommendations
- **Edit CV**: Modify existing CVs based on user instructions

### 🎯 Job-CV Matching
- Upload CV to find matching jobs
- AI compatibility scoring (0-100%)
- Detailed match analysis with missing/matching skills
- Prioritized job recommendations

### 👨‍💼 Admin Dashboard
- User management and statistics
- Usage tracking and limits
- Ban/unban functionality
- Export user data

## 🚀 Quick Start

### 1. Prerequisites
- Python 3.8+
- Telegram Bot Token
- OpenAI API Key
- Google Gemini API Key

### 2. Installation

```bash
# Clone the repository
git clone <repository-url>
cd telegram-job-bot

# Install dependencies
pip install -r requirements.txt
```

### 3. Configuration

1. Copy `.env.example` to `.env`:
```bash
cp .env.example .env
```

2. Edit `.env` file with your API keys:
```env
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
OPENAI_API_KEY=your_openai_api_key_here
GEMINI_API_KEY=your_gemini_api_key_here
```

3. Add your Telegram ID to `config/admin_ids.py`:
```python
ADMIN_IDS = [
    123456789,  # Replace with your Telegram ID
]
```

### 4. Getting API Keys

#### Telegram Bot Token
1. Message [@BotFather](https://t.me/botfather) on Telegram
2. Send `/newbot` and follow instructions
3. Copy the bot token

#### OpenAI API Key
1. Go to [OpenAI API](https://platform.openai.com/api-keys)
2. Create a new API key
3. Copy the key

#### Google Gemini API Key
1. Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create a new API key
3. Copy the key

### 5. Run the Bot

```bash
python main.py
```

## 📁 Project Structure

```
├── main.py                    # Entry point
├── config/
│   ├── settings.py           # Configuration settings
│   └── admin_ids.py          # Admin Telegram IDs
├── bot/
│   ├── handlers.py           # Bot handlers
│   ├── keyboards.py          # Inline keyboards
│   └── states.py             # Conversation states
├── services/
│   ├── job_scraper.py        # LinkedIn scraping
│   ├── cv_processor.py       # CV operations
│   ├── ai_analyzer.py        # AI integration
│   └── job_matcher.py        # Job-CV matching
├── database/
│   ├── user_manager.py       # User management
│   └── excel_handler.py      # Excel operations
├── admin/
│   └── dashboard.py          # Admin dashboard
├── utils/
│   ├── logger.py             # Logging
│   ├── validators.py         # Input validation
│   └── exporters.py          # Export utilities
└── data/                     # Database files
```

## 🎮 Usage

### For Users

1. **Start the bot**: Send `/start`
2. **Job Search**: 
   - Click "🔍 Job Search"
   - Select category, country, and job type
   - View and export results
3. **CV Assistant**:
   - Click "📄 CV Assistant"
   - Choose create, analyze, or edit
   - Follow the prompts
4. **Job Matching**:
   - Click "🎯 Job-CV Matching"
   - Upload your CV
   - View compatibility scores

### For Admins

1. **Access admin panel**: Send `/admin`
2. **View statistics**: Click "📊 Statistics"
3. **Manage users**: Click "👥 View Users"
4. **Search users**: Click "🔍 Search User"

## 🔧 Configuration

### Usage Limits
Edit `config/settings.py`:
```python
USAGE_LIMITS = {
    'free': 3,      # 3 daily uses
    'pro': 20,      # 20 daily uses
    'premium': float('inf')  # Unlimited
}
```

### Job Categories
Add/modify job categories in `config/settings.py`:
```python
JOB_CATEGORIES = [
    'Front End',
    'Back End',
    'Data Analysis',
    # Add more categories
]
```

## 🛠️ Development

### Adding New Features

1. **New Command**: Add handler in `bot/handlers.py`
2. **New State**: Add to `bot/states.py`
3. **New Keyboard**: Add to `bot/keyboards.py`
4. **New Service**: Create in `services/`

### Testing

```bash
# Run tests (when available)
python -m pytest tests/

# Check code style
black .
flake8 .
```

## 📊 Monitoring

### Logs
- Bot logs are stored in `logs/` directory
- Log level can be configured in `utils/logger.py`

### Database
- User data: `data/users.xlsx`
- Usage logs: `data/usage_logs.xlsx`

## 🔒 Security

- Admin IDs are stored separately in `config/admin_ids.py`
- API keys are stored in `.env` file (not committed)
- User data is stored locally in Excel files
- Input validation for all user inputs

## 🚨 Troubleshooting

### Common Issues

1. **Bot not responding**:
   - Check bot token in `.env`
   - Verify internet connection
   - Check logs for errors

2. **AI features not working**:
   - Verify API keys in `.env`
   - Check API quotas/limits
   - Review error logs

3. **Job search not working**:
   - LinkedIn may block scraping
   - Check Chrome/ChromeDriver installation
   - Review scraping logs

### Getting Help

1. Check logs in `logs/` directory
2. Review error messages
3. Verify configuration files
4. Check API key validity

## 📝 License

This project is licensed under the MIT License.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📞 Support

For support and questions:
- Check the documentation
- Review troubleshooting section
- Create an issue on GitHub
