"""
CV processing service for creation, analysis, and editing
"""
import os
import io
from datetime import datetime
from typing import Dict, List, Optional, Any
from docx import Document
from docx.shared import Inches
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.style import WD_STYLE_TYPE
import PyPDF2
from reportlab.lib.pagesizes import letter
from reportlab.platypus import SimpleDocT<PERSON>plate, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from utils.logger import logger
from utils.validators import sanitize_filename

class CVProcessor:
    """Handle CV creation, analysis, and editing operations"""
    
    def __init__(self):
        self.temp_dir = "temp"
        self.exports_dir = "exports"
        os.makedirs(self.temp_dir, exist_ok=True)
        os.makedirs(self.exports_dir, exist_ok=True)
    
    def extract_text_from_pdf(self, file_path: str) -> str:
        """Extract text from PDF file"""
        try:
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                text = ""
                
                for page in pdf_reader.pages:
                    text += page.extract_text() + "\n"
                
                return text.strip()
                
        except Exception as e:
            logger.error(f"Error extracting text from PDF: {e}")
            return ""
    
    def extract_text_from_docx(self, file_path: str) -> str:
        """Extract text from Word document"""
        try:
            doc = Document(file_path)
            text = ""
            
            for paragraph in doc.paragraphs:
                text += paragraph.text + "\n"
            
            # Extract text from tables
            for table in doc.tables:
                for row in table.rows:
                    for cell in row.cells:
                        text += cell.text + " "
                    text += "\n"
            
            return text.strip()
            
        except Exception as e:
            logger.error(f"Error extracting text from DOCX: {e}")
            return ""
    
    def extract_text_from_file(self, file_path: str) -> str:
        """Extract text from CV file (PDF or DOCX)"""
        try:
            file_extension = file_path.lower().split('.')[-1]
            
            if file_extension == 'pdf':
                return self.extract_text_from_pdf(file_path)
            elif file_extension in ['doc', 'docx']:
                return self.extract_text_from_docx(file_path)
            else:
                logger.error(f"Unsupported file format: {file_extension}")
                return ""
                
        except Exception as e:
            logger.error(f"Error extracting text from file: {e}")
            return ""
    
    def create_cv_docx(self, cv_data: Dict[str, Any], user_id: int) -> str:
        """Create CV in Word format"""
        try:
            # Create new document
            doc = Document()
            
            # Add title
            title = doc.add_heading(f"{cv_data.get('first_name', '')} {cv_data.get('last_name', '')}", 0)
            title.alignment = WD_ALIGN_PARAGRAPH.CENTER
            
            # Add contact information
            contact_info = doc.add_paragraph()
            contact_info.alignment = WD_ALIGN_PARAGRAPH.CENTER
            
            contact_details = []
            if cv_data.get('email'):
                contact_details.append(f"📧 {cv_data['email']}")
            if cv_data.get('phone'):
                contact_details.append(f"📱 {cv_data['phone']}")
            if cv_data.get('location'):
                contact_details.append(f"📍 {cv_data['location']}")
            
            contact_info.add_run(" | ".join(contact_details))
            
            # Add professional summary
            if cv_data.get('summary'):
                doc.add_heading('Professional Summary', level=1)
                doc.add_paragraph(cv_data['summary'])
            
            # Add experience
            if cv_data.get('experience'):
                doc.add_heading('Work Experience', level=1)
                for exp in cv_data['experience']:
                    exp_para = doc.add_paragraph()
                    exp_para.add_run(f"{exp.get('title', '')} at {exp.get('company', '')}").bold = True
                    exp_para.add_run(f"\n{exp.get('duration', '')}")
                    if exp.get('description'):
                        doc.add_paragraph(exp['description'])
                    doc.add_paragraph()  # Add space
            
            # Add education
            if cv_data.get('education'):
                doc.add_heading('Education', level=1)
                for edu in cv_data['education']:
                    edu_para = doc.add_paragraph()
                    edu_para.add_run(f"{edu.get('degree', '')} - {edu.get('institution', '')}").bold = True
                    edu_para.add_run(f"\n{edu.get('year', '')}")
                    if edu.get('details'):
                        doc.add_paragraph(edu['details'])
                    doc.add_paragraph()  # Add space
            
            # Add skills
            if cv_data.get('skills'):
                doc.add_heading('Skills', level=1)
                skills_para = doc.add_paragraph()
                
                # Group skills by category if provided
                if isinstance(cv_data['skills'], dict):
                    for category, skills_list in cv_data['skills'].items():
                        skills_para.add_run(f"{category}: ").bold = True
                        skills_para.add_run(", ".join(skills_list) + "\n")
                else:
                    # Simple list of skills
                    skills_para.add_run(", ".join(cv_data['skills']))
            
            # Add languages
            if cv_data.get('languages'):
                doc.add_heading('Languages', level=1)
                lang_para = doc.add_paragraph()
                for lang in cv_data['languages']:
                    lang_para.add_run(f"{lang.get('language', '')}: {lang.get('level', '')}\n")
            
            # Save document
            filename = f"CV_{user_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.docx"
            file_path = os.path.join(self.exports_dir, filename)
            doc.save(file_path)
            
            logger.info(f"CV created successfully: {file_path}")
            return file_path
            
        except Exception as e:
            logger.error(f"Error creating CV DOCX: {e}")
            return ""
    
    def create_cv_pdf(self, cv_data: Dict[str, Any], user_id: int) -> str:
        """Create CV in PDF format"""
        try:
            filename = f"CV_{user_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
            file_path = os.path.join(self.exports_dir, filename)
            
            # Create PDF document
            doc = SimpleDocTemplate(file_path, pagesize=letter)
            styles = getSampleStyleSheet()
            story = []
            
            # Custom styles
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontSize=18,
                spaceAfter=30,
                alignment=1  # Center alignment
            )
            
            heading_style = ParagraphStyle(
                'CustomHeading',
                parent=styles['Heading2'],
                fontSize=14,
                spaceAfter=12,
                textColor='blue'
            )
            
            # Add title
            title = f"{cv_data.get('first_name', '')} {cv_data.get('last_name', '')}"
            story.append(Paragraph(title, title_style))
            
            # Add contact information
            contact_details = []
            if cv_data.get('email'):
                contact_details.append(f"Email: {cv_data['email']}")
            if cv_data.get('phone'):
                contact_details.append(f"Phone: {cv_data['phone']}")
            if cv_data.get('location'):
                contact_details.append(f"Location: {cv_data['location']}")
            
            contact_text = " | ".join(contact_details)
            story.append(Paragraph(contact_text, styles['Normal']))
            story.append(Spacer(1, 12))
            
            # Add professional summary
            if cv_data.get('summary'):
                story.append(Paragraph("Professional Summary", heading_style))
                story.append(Paragraph(cv_data['summary'], styles['Normal']))
                story.append(Spacer(1, 12))
            
            # Add experience
            if cv_data.get('experience'):
                story.append(Paragraph("Work Experience", heading_style))
                for exp in cv_data['experience']:
                    exp_title = f"<b>{exp.get('title', '')} at {exp.get('company', '')}</b>"
                    story.append(Paragraph(exp_title, styles['Normal']))
                    story.append(Paragraph(exp.get('duration', ''), styles['Normal']))
                    if exp.get('description'):
                        story.append(Paragraph(exp['description'], styles['Normal']))
                    story.append(Spacer(1, 6))
            
            # Add education
            if cv_data.get('education'):
                story.append(Paragraph("Education", heading_style))
                for edu in cv_data['education']:
                    edu_title = f"<b>{edu.get('degree', '')} - {edu.get('institution', '')}</b>"
                    story.append(Paragraph(edu_title, styles['Normal']))
                    story.append(Paragraph(edu.get('year', ''), styles['Normal']))
                    if edu.get('details'):
                        story.append(Paragraph(edu['details'], styles['Normal']))
                    story.append(Spacer(1, 6))
            
            # Add skills
            if cv_data.get('skills'):
                story.append(Paragraph("Skills", heading_style))
                if isinstance(cv_data['skills'], dict):
                    for category, skills_list in cv_data['skills'].items():
                        skills_text = f"<b>{category}:</b> {', '.join(skills_list)}"
                        story.append(Paragraph(skills_text, styles['Normal']))
                else:
                    skills_text = ", ".join(cv_data['skills'])
                    story.append(Paragraph(skills_text, styles['Normal']))
                story.append(Spacer(1, 6))
            
            # Build PDF
            doc.build(story)
            
            logger.info(f"CV PDF created successfully: {file_path}")
            return file_path
            
        except Exception as e:
            logger.error(f"Error creating CV PDF: {e}")
            return ""
    
    def parse_cv_data(self, cv_text: str) -> Dict[str, Any]:
        """Parse CV text and extract structured data"""
        try:
            # This is a simplified parser
            # In production, you might want to use more sophisticated NLP
            
            lines = cv_text.split('\n')
            cv_data = {
                'personal_info': {},
                'experience': [],
                'education': [],
                'skills': [],
                'languages': []
            }
            
            current_section = None
            
            for line in lines:
                line = line.strip()
                if not line:
                    continue
                
                # Detect sections
                line_lower = line.lower()
                if any(keyword in line_lower for keyword in ['experience', 'work', 'employment']):
                    current_section = 'experience'
                elif any(keyword in line_lower for keyword in ['education', 'academic', 'qualification']):
                    current_section = 'education'
                elif any(keyword in line_lower for keyword in ['skill', 'competenc', 'technical']):
                    current_section = 'skills'
                elif any(keyword in line_lower for keyword in ['language', 'linguistic']):
                    current_section = 'languages'
                elif '@' in line:  # Email detection
                    cv_data['personal_info']['email'] = line
                elif any(char.isdigit() for char in line) and len(line) > 8:  # Phone detection
                    cv_data['personal_info']['phone'] = line
                
                # Add content to current section
                if current_section and line not in cv_data[current_section]:
                    if current_section == 'skills':
                        # Split skills by common delimiters
                        skills = [skill.strip() for skill in line.replace(',', '|').replace(';', '|').split('|')]
                        cv_data[current_section].extend(skills)
                    else:
                        cv_data[current_section].append(line)
            
            return cv_data
            
        except Exception as e:
            logger.error(f"Error parsing CV data: {e}")
            return {}
    
    def edit_cv(self, original_file_path: str, edit_instructions: str, user_id: int) -> str:
        """Edit existing CV based on instructions"""
        try:
            # Extract text from original CV
            original_text = self.extract_text_from_file(original_file_path)
            
            if not original_text:
                logger.error("Could not extract text from original CV")
                return ""
            
            # Parse CV data
            cv_data = self.parse_cv_data(original_text)
            
            # Apply edit instructions (simplified)
            # In production, you would use AI to interpret and apply edits
            edited_cv_data = self._apply_edit_instructions(cv_data, edit_instructions)
            
            # Create new CV
            return self.create_cv_docx(edited_cv_data, user_id)
            
        except Exception as e:
            logger.error(f"Error editing CV: {e}")
            return ""
    
    def _apply_edit_instructions(self, cv_data: Dict[str, Any], instructions: str) -> Dict[str, Any]:
        """Apply edit instructions to CV data"""
        # This is a simplified implementation
        # In production, you would use AI to interpret and apply complex edits
        
        edited_data = cv_data.copy()
        instructions_lower = instructions.lower()
        
        # Simple keyword-based edits
        if 'add skill' in instructions_lower:
            # Extract skills to add
            skills_to_add = self._extract_skills_from_text(instructions)
            if 'skills' not in edited_data:
                edited_data['skills'] = []
            edited_data['skills'].extend(skills_to_add)
        
        if 'remove' in instructions_lower:
            # Handle removal instructions
            pass
        
        if 'update' in instructions_lower or 'change' in instructions_lower:
            # Handle update instructions
            pass
        
        return edited_data
    
    def _extract_skills_from_text(self, text: str) -> List[str]:
        """Extract skills from instruction text"""
        # Simple skill extraction
        common_skills = [
            'python', 'java', 'javascript', 'react', 'node.js', 'sql', 'html', 'css',
            'project management', 'leadership', 'communication', 'teamwork'
        ]
        
        found_skills = []
        text_lower = text.lower()
        
        for skill in common_skills:
            if skill in text_lower:
                found_skills.append(skill.title())
        
        return found_skills
    
    def cleanup_temp_files(self, user_id: int):
        """Clean up temporary files for a user"""
        try:
            temp_pattern = f"{user_id}_"
            for filename in os.listdir(self.temp_dir):
                if filename.startswith(temp_pattern):
                    file_path = os.path.join(self.temp_dir, filename)
                    os.remove(file_path)
                    logger.info(f"Cleaned up temp file: {filename}")
        except Exception as e:
            logger.error(f"Error cleaning up temp files: {e}")
