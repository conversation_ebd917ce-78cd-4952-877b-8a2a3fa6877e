"""
AI integration service for CV analysis and job matching
"""
import openai
import google.generativeai as genai
from typing import Dict, List, Optional, Any
from config.settings import Config
from utils.logger import logger
import json

class AIAnalyzer:
    """AI service for CV analysis and job matching"""
    
    def __init__(self):
        self.setup_apis()
    
    def setup_apis(self):
        """Setup AI API clients"""
        try:
            # Setup OpenRouter (OpenAI compatible)
            if Config.OPENAI_API_KEY:
                openai.api_key = Config.OPENAI_API_KEY
                openai.base_url = "https://openrouter.ai/api/v1"
                logger.info("OpenRouter API configured")

            # Setup Google Gemini
            if Config.GEMINI_API_KEY:
                genai.configure(api_key=Config.GEMINI_API_KEY)
                self.gemini_model = genai.GenerativeModel('gemini-pro')
                logger.info("Gemini API configured")

        except Exception as e:
            logger.error(f"Error setting up AI APIs: {e}")
    
    def analyze_cv(self, cv_text: str) -> Dict[str, Any]:
        """
        Analyze CV content using AI
        
        Args:
            cv_text: Extracted text from CV
            
        Returns:
            Analysis results dictionary
        """
        try:
            # Try Gemini first, fallback to OpenAI
            if hasattr(self, 'gemini_model'):
                return self._analyze_cv_gemini(cv_text)
            else:
                return self._analyze_cv_openai(cv_text)
                
        except Exception as e:
            logger.error(f"Error analyzing CV: {e}")
            return self._get_default_analysis()
    
    def _analyze_cv_gemini(self, cv_text: str) -> Dict[str, Any]:
        """Analyze CV using Google Gemini"""
        prompt = f"""
        Analyze the following CV and provide a comprehensive analysis in JSON format:

        CV Content:
        {cv_text}

        Please provide analysis in the following JSON structure:
        {{
            "strengths": ["list of strengths"],
            "weaknesses": ["list of areas for improvement"],
            "skills_assessment": {{
                "technical_skills": ["list of technical skills found"],
                "soft_skills": ["list of soft skills found"],
                "missing_skills": ["list of commonly expected skills that are missing"]
            }},
            "experience_analysis": {{
                "years_of_experience": "estimated years",
                "career_progression": "assessment of career growth",
                "industry_focus": "primary industry/domain"
            }},
            "recommendations": ["list of specific improvement recommendations"],
            "overall_score": "score out of 100",
            "summary": "brief overall assessment"
        }}
        """
        
        try:
            response = self.gemini_model.generate_content(prompt)
            
            # Extract JSON from response
            response_text = response.text
            
            # Try to parse JSON from the response
            json_start = response_text.find('{')
            json_end = response_text.rfind('}') + 1
            
            if json_start != -1 and json_end != -1:
                json_str = response_text[json_start:json_end]
                analysis = json.loads(json_str)
                return analysis
            else:
                # Fallback to structured parsing
                return self._parse_gemini_response(response_text)
                
        except Exception as e:
            logger.error(f"Error with Gemini CV analysis: {e}")
            return self._get_default_analysis()
    
    def _analyze_cv_openai(self, cv_text: str) -> Dict[str, Any]:
        """Analyze CV using OpenAI"""
        prompt = f"""
        Analyze the following CV and provide a comprehensive analysis:

        CV Content:
        {cv_text}

        Please provide analysis in JSON format with the following structure:
        - strengths: list of strengths
        - weaknesses: list of areas for improvement  
        - skills_assessment: technical skills, soft skills, missing skills
        - experience_analysis: years of experience, career progression, industry focus
        - recommendations: specific improvement recommendations
        - overall_score: score out of 100
        - summary: brief overall assessment
        """
        
        try:
            from openai import OpenAI
            client = OpenAI(
                api_key=Config.OPENAI_API_KEY,
                base_url="https://openrouter.ai/api/v1"
            )

            response = client.chat.completions.create(
                model="meta-llama/llama-3.1-8b-instruct:free",
                messages=[
                    {"role": "system", "content": "You are an expert HR consultant and CV analyzer."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=1500,
                temperature=0.7
            )
            
            response_text = response.choices[0].message.content
            
            # Try to parse JSON from response
            try:
                return json.loads(response_text)
            except:
                return self._parse_openai_response(response_text)
                
        except Exception as e:
            logger.error(f"Error with OpenAI CV analysis: {e}")
            return self._get_default_analysis()
    
    def match_jobs_to_cv(self, cv_text: str, jobs: List[Dict[str, str]]) -> List[Dict[str, Any]]:
        """
        Match jobs to CV and calculate compatibility scores
        
        Args:
            cv_text: CV content
            jobs: List of job dictionaries
            
        Returns:
            List of jobs with compatibility scores
        """
        try:
            matched_jobs = []
            
            for job in jobs:
                compatibility = self._calculate_job_compatibility(cv_text, job)
                
                job_with_score = job.copy()
                job_with_score.update(compatibility)
                matched_jobs.append(job_with_score)
            
            # Sort by compatibility score (highest first)
            matched_jobs.sort(key=lambda x: x.get('compatibility_score', 0), reverse=True)
            
            return matched_jobs
            
        except Exception as e:
            logger.error(f"Error matching jobs to CV: {e}")
            return jobs
    
    def _calculate_job_compatibility(self, cv_text: str, job: Dict[str, str]) -> Dict[str, Any]:
        """Calculate compatibility between CV and job"""
        try:
            job_description = f"""
            Job Title: {job.get('title', '')}
            Company: {job.get('company', '')}
            Location: {job.get('location', '')}
            Description: {job.get('description', '')}
            Requirements: {job.get('requirements', '')}
            """
            
            prompt = f"""
            Calculate the compatibility between this CV and job posting. Provide a score from 0-100 and explanation.

            CV:
            {cv_text[:2000]}  # Limit CV text length

            Job Posting:
            {job_description}

            Provide response in JSON format:
            {{
                "compatibility_score": 85,
                "matching_skills": ["skill1", "skill2"],
                "missing_skills": ["skill3", "skill4"],
                "explanation": "Brief explanation of the match",
                "recommendation": "Why this job is/isn't a good fit"
            }}
            """
            
            if hasattr(self, 'gemini_model'):
                response = self.gemini_model.generate_content(prompt)
                response_text = response.text
            else:
                response = openai.ChatCompletion.create(
                    model="gpt-3.5-turbo",
                    messages=[
                        {"role": "system", "content": "You are an expert job matching consultant."},
                        {"role": "user", "content": prompt}
                    ],
                    max_tokens=500,
                    temperature=0.5
                )
                response_text = response.choices[0].message.content
            
            # Parse JSON response
            try:
                json_start = response_text.find('{')
                json_end = response_text.rfind('}') + 1
                if json_start != -1 and json_end != -1:
                    json_str = response_text[json_start:json_end]
                    return json.loads(json_str)
            except:
                pass
            
            # Fallback to default scoring
            return {
                'compatibility_score': 50,
                'matching_skills': [],
                'missing_skills': [],
                'explanation': 'Basic compatibility analysis',
                'recommendation': 'Consider applying based on your background'
            }
            
        except Exception as e:
            logger.error(f"Error calculating job compatibility: {e}")
            return {
                'compatibility_score': 0,
                'matching_skills': [],
                'missing_skills': [],
                'explanation': 'Unable to analyze compatibility',
                'recommendation': 'Manual review recommended'
            }
    
    def generate_cv_improvements(self, cv_text: str, target_job: str = None) -> Dict[str, Any]:
        """Generate CV improvement suggestions"""
        try:
            target_context = f" for {target_job} positions" if target_job else ""
            
            prompt = f"""
            Provide specific CV improvement suggestions{target_context}:

            Current CV:
            {cv_text}

            Provide suggestions in JSON format:
            {{
                "content_improvements": ["specific content suggestions"],
                "formatting_suggestions": ["formatting improvements"],
                "skill_additions": ["skills to add or highlight"],
                "experience_enhancements": ["how to better present experience"],
                "keyword_optimization": ["important keywords to include"]
            }}
            """
            
            if hasattr(self, 'gemini_model'):
                response = self.gemini_model.generate_content(prompt)
                response_text = response.text
            else:
                response = openai.ChatCompletion.create(
                    model="gpt-3.5-turbo",
                    messages=[
                        {"role": "system", "content": "You are an expert CV writing consultant."},
                        {"role": "user", "content": prompt}
                    ],
                    max_tokens=800,
                    temperature=0.7
                )
                response_text = response.choices[0].message.content
            
            # Parse JSON response
            try:
                json_start = response_text.find('{')
                json_end = response_text.rfind('}') + 1
                if json_start != -1 and json_end != -1:
                    json_str = response_text[json_start:json_end]
                    return json.loads(json_str)
            except:
                pass
            
            return self._get_default_improvements()
            
        except Exception as e:
            logger.error(f"Error generating CV improvements: {e}")
            return self._get_default_improvements()
    
    def _parse_gemini_response(self, response_text: str) -> Dict[str, Any]:
        """Parse Gemini response when JSON parsing fails"""
        # Implement basic text parsing logic
        return self._get_default_analysis()
    
    def _parse_openai_response(self, response_text: str) -> Dict[str, Any]:
        """Parse OpenAI response when JSON parsing fails"""
        # Implement basic text parsing logic
        return self._get_default_analysis()
    
    def _get_default_analysis(self) -> Dict[str, Any]:
        """Get default analysis structure"""
        return {
            "strengths": ["Professional presentation", "Clear structure"],
            "weaknesses": ["Could benefit from more specific achievements", "Consider adding more keywords"],
            "skills_assessment": {
                "technical_skills": ["Various technical skills identified"],
                "soft_skills": ["Communication", "Problem-solving"],
                "missing_skills": ["Industry-specific skills may be needed"]
            },
            "experience_analysis": {
                "years_of_experience": "Multiple years",
                "career_progression": "Shows growth potential",
                "industry_focus": "Professional background"
            },
            "recommendations": [
                "Add quantifiable achievements",
                "Include relevant keywords",
                "Highlight key accomplishments"
            ],
            "overall_score": "75",
            "summary": "Solid CV with room for targeted improvements"
        }
    
    def _get_default_improvements(self) -> Dict[str, Any]:
        """Get default improvement suggestions"""
        return {
            "content_improvements": [
                "Add quantifiable achievements and metrics",
                "Include more action verbs",
                "Highlight key accomplishments"
            ],
            "formatting_suggestions": [
                "Ensure consistent formatting",
                "Use bullet points effectively",
                "Maintain professional layout"
            ],
            "skill_additions": [
                "Include relevant technical skills",
                "Add industry-specific keywords",
                "Highlight soft skills"
            ],
            "experience_enhancements": [
                "Use STAR method for achievements",
                "Quantify results where possible",
                "Focus on relevant experience"
            ],
            "keyword_optimization": [
                "Research job-specific keywords",
                "Include industry terminology",
                "Use relevant skill keywords"
            ]
        }
