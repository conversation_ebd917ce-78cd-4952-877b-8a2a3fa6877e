# 🤖 بوت البحث الذكي عن الوظائف

## ✅ تم الإعداد بنجاح!

تم إعداد البوت بالمعلومات التالية:
- **🔑 توكن البوت:** `8164230719:AAEI5fGcBHgznGh5CmR2_EIsmACLyZxiLAc`
- **🤖 OpenRouter API:** `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`
- **🧠 Google Gemini API:** `AIzaSyA-i1f2UYV6QMc6a53ye5FUsMfcH9vaesE`
- **👨‍💼 المشرف:** `@Abdoo_mahm` (ID: `1732487077`)

## 🚀 كيفية تشغيل البوت

### الطريقة الأولى: ملف Batch
```bash
# انقر مرتين على الملف
run_bot.bat
```

### الطريقة الثانية: سطر الأوامر
```bash
# إذا كان لديك Python 3.13
py -3.13 main.py

# أو
python main.py
```

## 🎯 ميزات البوت

### 🔍 البحث عن الوظائف
- البحث حسب الفئة: تطوير الواجهات، تطوير الخلفية، تحليل البيانات، التسويق، تطوير المواقع، هندسة البيانات
- البحث حسب الدولة: مصر، السعودية، الإمارات، عن بُعد
- البحث حسب نوع العمل: دوام كامل، دوام جزئي، تدريب، عن بُعد
- تصدير النتائج إلى Excel

### 📄 مساعد السيرة الذاتية
- **إنشاء سيرة ذاتية جديدة:** إنشاء سيرة ذاتية احترافية خطوة بخطوة
- **تحليل السيرة الذاتية:** تحليل بالذكاء الاصطناعي مع نقاط القوة والضعف والتوصيات
- **تعديل السيرة الذاتية:** تعديل السيرة الذاتية الحالية حسب التعليمات

### 🎯 مطابقة الوظائف
- رفع السيرة الذاتية للعثور على الوظائف المناسبة
- درجات التوافق بالذكاء الاصطناعي (0-100%)
- تحليل مفصل للمهارات المطابقة والمفقودة
- ترتيب الوظائف حسب الأولوية

### 👨‍💼 لوحة الإدارة (للمشرفين فقط)
- عرض وإدارة المستخدمين
- إحصائيات الاستخدام
- إدارة الحظر
- البحث في المستخدمين

## 📱 كيفية استخدام البوت

### للمستخدمين العاديين:

1. **ابدأ المحادثة:**
   - ابحث عن البوت في تليجرام
   - أرسل `/start`

2. **البحث عن الوظائف:**
   - اضغط "🔍 البحث عن الوظائف"
   - اختر الفئة والدولة ونوع العمل
   - اعرض النتائج وصدّرها

3. **مساعد السيرة الذاتية:**
   - اضغط "📄 مساعد السيرة الذاتية"
   - اختر إنشاء أو تحليل أو تعديل
   - اتبع التعليمات

4. **مطابقة الوظائف:**
   - اضغط "🎯 مطابقة الوظائف"
   - ارفع سيرتك الذاتية
   - اعرض درجات التوافق

### للمشرفين:

1. **الوصول للوحة الإدارة:**
   - أرسل `/admin`
   - أو اضغط "⚙️ لوحة الإدارة"

2. **عرض الإحصائيات:**
   - اضغط "📊 الإحصائيات"

3. **إدارة المستخدمين:**
   - اضغط "👥 عرض المستخدمين"
   - أو "🔍 البحث عن مستخدم"

## 🔧 حدود الاستخدام

- **المجاني:** 3 استخدامات يومياً
- **المدفوع Pro:** 20 استخدام يومياً  
- **المدفوع Premium:** استخدام غير محدود

## 📁 ملفات المشروع

```
├── main.py                    # نقطة البداية
├── .env                       # مفاتيح API (تم إعدادها)
├── run_bot.bat               # ملف التشغيل
├── config/                   # الإعدادات
├── bot/                      # منطق البوت
├── services/                 # الخدمات (AI، البحث، السيرة الذاتية)
├── database/                 # إدارة قاعدة البيانات
├── admin/                    # لوحة الإدارة
├── utils/                    # أدوات مساعدة
├── data/                     # ملفات قاعدة البيانات
├── temp/                     # ملفات مؤقتة
├── exports/                  # ملفات التصدير
└── logs/                     # ملفات السجلات
```

## 🛠️ استكشاف الأخطاء

### البوت لا يستجيب:
1. تحقق من توكن البوت في `.env`
2. تأكد من الاتصال بالإنترنت
3. راجع ملفات السجلات في `logs/`

### ميزات الذكاء الاصطناعي لا تعمل:
1. تحقق من مفاتيح API في `.env`
2. تأكد من صحة المفاتيح
3. تحقق من حدود الاستخدام للـ APIs

### البحث عن الوظائف لا يعمل:
1. LinkedIn قد يحجب البحث الآلي
2. تحقق من تثبيت Chrome/ChromeDriver
3. راجع سجلات البحث

## 📞 الدعم

للحصول على المساعدة:
1. راجع ملفات السجلات في `logs/`
2. تحقق من رسائل الخطأ
3. تأكد من صحة ملفات الإعداد
4. تحقق من صحة مفاتيح API

## 🎉 البوت جاهز للاستخدام!

البوت يعمل الآن ويمكن للمستخدمين التفاعل معه على تليجرام.

**رابط البوت:** `https://t.me/YourBotUsername`

---

**تم إنشاؤه بواسطة:** Augment Agent  
**التاريخ:** يناير 2025
