@echo off
echo Starting Telegram Job Bot...
echo.

REM Check if Python is installed
python --version
if %errorlevel% neq 0 (
    echo Error: Python is not installed or not in PATH
    pause
    exit /b 1
)

REM Check if .env file exists
if not exist .env (
    echo Warning: .env file not found
    echo Please copy .env.example to .env and add your API keys
    pause
)

REM Create required directories
if not exist data mkdir data
if not exist temp mkdir temp
if not exist exports mkdir exports
if not exist logs mkdir logs

REM Run the bot
echo Running the bot...
python main.py

pause
