@echo off
echo 🤖 بدء تشغيل بوت البحث عن الوظائف...
echo.

REM Check if Python 3.13 is available
py -3.13 --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ تم العثور على Python 3.13
    set PYTHON_CMD=py -3.13
) else (
    echo ⚠️ Python 3.13 غير متوفر، جاري المحاولة مع python
    python --version >nul 2>&1
    if %errorlevel% neq 0 (
        echo ❌ خطأ: Python غير مثبت أو غير موجود في PATH
        pause
        exit /b 1
    )
    set PYTHON_CMD=python
)

REM Check if .env file exists
if not exist .env (
    echo ⚠️ تحذير: ملف .env غير موجود
    echo يرجى نسخ .env.example إلى .env وإضافة مفاتيح API الخاصة بك
    echo.
    echo المفاتيح المطلوبة:
    echo - TELEGRAM_BOT_TOKEN (من @BotFather)
    echo - OPENAI_API_KEY (من OpenRouter)
    echo - GEMINI_API_KEY (من Google AI Studio)
    echo.
    pause
)

REM Create required directories
if not exist data mkdir data
if not exist temp mkdir temp
if not exist exports mkdir exports
if not exist logs mkdir logs

REM Display current configuration
echo.
echo 📋 الإعدادات الحالية:
if exist .env (
    echo ✅ ملف .env موجود
) else (
    echo ❌ ملف .env مفقود
)

echo.
echo 🚀 تشغيل البوت...
echo 📝 للإيقاف: اضغط Ctrl+C
echo.

REM Run the bot
%PYTHON_CMD% main.py

echo.
echo 🛑 تم إيقاف البوت
pause
