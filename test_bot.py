"""
Test script to check if the bot can start
"""
import os
import sys

def test_imports():
    """Test if all required modules can be imported"""
    try:
        print("Testing imports...")
        
        # Test basic imports
        import telegram
        print("✅ telegram imported successfully")
        
        import pandas
        print("✅ pandas imported successfully")
        
        import selenium
        print("✅ selenium imported successfully")
        
        import openai
        print("✅ openai imported successfully")
        
        import google.generativeai
        print("✅ google.generativeai imported successfully")
        
        # Test our modules
        from config.settings import Config
        print("✅ Config imported successfully")
        
        from database.user_manager import UserManager
        print("✅ UserManager imported successfully")
        
        from bot.handlers import BotHandlers
        print("✅ BotHandlers imported successfully")
        
        print("\n🎉 All imports successful!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_config():
    """Test configuration"""
    try:
        print("\nTesting configuration...")
        
        from config.settings import Config
        
        # Check if .env file exists
        if os.path.exists('.env'):
            print("✅ .env file found")
        else:
            print("⚠️ .env file not found - you need to create it")
        
        # Test config validation (will fail without real tokens)
        try:
            Config.validate_config()
            print("✅ Configuration is valid")
        except ValueError as e:
            print(f"⚠️ Configuration issue: {e}")
            print("   This is expected if you haven't set up API keys yet")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        return False

def test_directories():
    """Test if required directories exist"""
    try:
        print("\nTesting directories...")
        
        required_dirs = ['data', 'temp', 'exports', 'logs']
        
        for dir_name in required_dirs:
            if os.path.exists(dir_name):
                print(f"✅ {dir_name}/ directory exists")
            else:
                os.makedirs(dir_name, exist_ok=True)
                print(f"✅ {dir_name}/ directory created")
        
        return True
        
    except Exception as e:
        print(f"❌ Directory error: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 Testing Telegram Job Bot Setup\n")
    
    tests = [
        test_imports,
        test_config,
        test_directories
    ]
    
    results = []
    for test in tests:
        results.append(test())
    
    print(f"\n📊 Test Results: {sum(results)}/{len(results)} passed")
    
    if all(results):
        print("\n🎉 All tests passed! Your bot setup looks good.")
        print("\n📝 Next steps:")
        print("1. Edit .env file with your API keys")
        print("2. Add your Telegram ID to config/admin_ids.py")
        print("3. Run: python main.py")
    else:
        print("\n❌ Some tests failed. Please fix the issues above.")

if __name__ == "__main__":
    main()
