"""
Conversation states for the Telegram bot
"""
from enum import Enum

class ConversationState(Enum):
    """Main conversation states"""
    MAIN_MENU = "main_menu"
    JOB_SEARCH = "job_search"
    CV_OPERATIONS = "cv_operations"
    ADMIN_PANEL = "admin_panel"

class JobSearchState(Enum):
    """Job search conversation states"""
    SELECTING_CATEGORY = "selecting_category"
    SELECTING_COUNTRY = "selecting_country"
    SELECTING_JOB_TYPE = "selecting_job_type"
    SEARCHING = "searching"
    VIEWING_RESULTS = "viewing_results"
    EXPORTING_RESULTS = "exporting_results"

class CVState(Enum):
    """CV operations conversation states"""
    SELECTING_OPERATION = "selecting_operation"
    
    # CV Creation states
    CREATING_CV = "creating_cv"
    COLLECTING_PERSONAL_INFO = "collecting_personal_info"
    COLLECTING_EXPERIENCE = "collecting_experience"
    COLLECTING_EDUCATION = "collecting_education"
    COLLECTING_SKILLS = "collecting_skills"
    GENERATING_CV = "generating_cv"
    
    # CV Analysis states
    ANALYZING_CV = "analyzing_cv"
    UPLOADING_CV_FOR_ANALYSIS = "uploading_cv_for_analysis"
    PROCESSING_ANALYSIS = "processing_analysis"
    
    # CV Editing states
    EDITING_CV = "editing_cv"
    UPLOADING_CV_FOR_EDIT = "uploading_cv_for_edit"
    COLLECTING_EDIT_REQUESTS = "collecting_edit_requests"
    PROCESSING_EDITS = "processing_edits"

class AdminState(Enum):
    """Admin panel conversation states"""
    MAIN_ADMIN_MENU = "main_admin_menu"
    VIEWING_USERS = "viewing_users"
    SEARCHING_USERS = "searching_users"
    EDITING_USER = "editing_user"
    VIEWING_STATS = "viewing_stats"
    MANAGING_BANS = "managing_bans"

class MatchingState(Enum):
    """Job-CV matching states"""
    UPLOADING_CV_FOR_MATCHING = "uploading_cv_for_matching"
    PROCESSING_MATCHING = "processing_matching"
    VIEWING_MATCHES = "viewing_matches"

# State transitions mapping
STATE_TRANSITIONS = {
    ConversationState.MAIN_MENU: [
        ConversationState.JOB_SEARCH,
        ConversationState.CV_OPERATIONS,
        ConversationState.ADMIN_PANEL
    ],
    ConversationState.JOB_SEARCH: [
        JobSearchState.SELECTING_CATEGORY,
        ConversationState.MAIN_MENU
    ],
    ConversationState.CV_OPERATIONS: [
        CVState.SELECTING_OPERATION,
        ConversationState.MAIN_MENU
    ],
    ConversationState.ADMIN_PANEL: [
        AdminState.MAIN_ADMIN_MENU,
        ConversationState.MAIN_MENU
    ]
}
