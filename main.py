"""
Telegram Job Bot - Main Entry Point
A comprehensive bot for job searching and CV analysis
"""
import asyncio
import sys
import os
from telegram import Update
from telegram.ext import Application, CommandHandler, CallbackQueryHandler, MessageHandler, filters
from config.settings import Config
from config.admin_ids import is_admin
from bot.handlers import BotHandlers
from database.user_manager import UserManager
from utils.logger import logger

class JobBot:
    """Main bot class"""
    
    def __init__(self):
        self.user_manager = UserManager()
        self.handlers = BotHandlers(self.user_manager)
        self.application = None
    
    def setup_application(self):
        """Setup the Telegram application"""
        try:
            # Validate configuration
            Config.validate_config()
            
            # Create application
            self.application = Application.builder().token(Config.TELEGRAM_BOT_TOKEN).build()
            
            # Add handlers
            self._add_handlers()
            
            logger.info("Bot application setup completed")
            
        except Exception as e:
            logger.error(f"Error setting up bot application: {e}")
            sys.exit(1)
    
    def _add_handlers(self):
        """Add all bot handlers"""
        # Command handlers
        self.application.add_handler(CommandHandler("start", self.handlers.start_command))
        self.application.add_handler(CommandHandler("help", self.handlers.help_command))
        self.application.add_handler(CommandHandler("admin", self.handlers.admin_command))
        self.application.add_handler(CommandHandler("stats", self.handlers.stats_command))
        
        # Callback query handlers
        self.application.add_handler(CallbackQueryHandler(self.handlers.handle_callback))
        
        # Message handlers
        self.application.add_handler(MessageHandler(
            filters.Document.ALL, self.handlers.handle_document
        ))
        self.application.add_handler(MessageHandler(
            filters.TEXT & ~filters.COMMAND, self.handlers.handle_text
        ))
        
        # Error handler
        self.application.add_error_handler(self.handlers.error_handler)
    
    async def start(self):
        """Start the bot"""
        try:
            logger.info("Starting Telegram Job Bot...")

            # Start the bot
            await self.application.run_polling()

        except KeyboardInterrupt:
            logger.info("Bot stopped by user")
        except Exception as e:
            logger.error(f"Error running bot: {e}")
        finally:
            await self.cleanup()

    async def cleanup(self):
        """Cleanup resources"""
        try:
            if self.application:
                await self.application.stop()
                await self.application.shutdown()
            logger.info("Bot cleanup completed")
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")

def main():
    """Main function"""
    try:
        # Create and setup bot
        bot = JobBot()
        bot.setup_application()

        print("🚀 بدء تشغيل بوت البحث عن الوظائف...")
        print("✅ تم إعداد البوت بنجاح!")
        print("📱 البوت جاهز للاستخدام على تليجرام")
        print("🛑 للإيقاف: اضغط Ctrl+C")

        # Run the bot
        bot.application.run_polling(allowed_updates=Update.ALL_TYPES)

    except KeyboardInterrupt:
        logger.info("Bot stopped by user")
        print("\n🛑 تم إيقاف البوت بواسطة المستخدم")
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        print(f"❌ خطأ: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
