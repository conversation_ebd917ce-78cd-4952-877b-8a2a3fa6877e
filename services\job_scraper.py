"""
LinkedIn job scraping service
"""
import time
import requests
from bs4 import <PERSON>Soup
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.service import Service
from typing import List, Dict, Optional
from utils.logger import logger
from config.settings import Config
import urllib.parse

class JobScraper:
    """LinkedIn job scraper"""
    
    def __init__(self):
        self.driver = None
        self.setup_driver()
    
    def setup_driver(self):
        """Setup Chrome WebDriver"""
        try:
            chrome_options = Options()
            chrome_options.add_argument("--headless")
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--window-size=1920,1080")
            chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
            
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            logger.info("Chrome WebDriver setup successfully")
            
        except Exception as e:
            logger.error(f"Error setting up WebDriver: {e}")
            self.driver = None
    
    def search_jobs(self, job_category: str, country: str, job_type: str) -> List[Dict[str, str]]:
        """
        Search for jobs on LinkedIn
        
        Args:
            job_category: Job category (e.g., 'Front End')
            country: Country (e.g., 'Egypt')
            job_type: Job type (e.g., 'Full-time')
            
        Returns:
            List of job dictionaries
        """
        try:
            if not self.driver:
                logger.error("WebDriver not initialized")
                return []
            
            # Construct search URL
            search_url = self._build_search_url(job_category, country, job_type)
            logger.info(f"Searching jobs with URL: {search_url}")
            
            # Navigate to search page
            self.driver.get(search_url)
            time.sleep(Config.SCRAPING_DELAY)
            
            # Wait for job listings to load
            try:
                WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, ".job-search-card"))
                )
            except:
                logger.warning("Job listings took too long to load")
            
            # Extract job data
            jobs = self._extract_job_data()
            logger.info(f"Found {len(jobs)} jobs")
            
            return jobs[:Config.MAX_JOBS_PER_SEARCH]
            
        except Exception as e:
            logger.error(f"Error searching jobs: {e}")
            return []
    
    def _build_search_url(self, job_category: str, country: str, job_type: str) -> str:
        """Build LinkedIn search URL"""
        base_url = "https://www.linkedin.com/jobs/search"
        
        # Map job types to LinkedIn filters
        job_type_mapping = {
            'full-time': 'F',
            'part-time': 'P',
            'internship': 'I',
            'remote': 'R'
        }
        
        # Map countries to location codes (simplified)
        location_mapping = {
            'egypt': 'Egypt',
            'saudi_arabia': 'Saudi Arabia',
            'uae': 'United Arab Emirates',
            'remote': 'Remote'
        }
        
        # Build query parameters
        params = {
            'keywords': job_category,
            'location': location_mapping.get(country.lower().replace(' ', '_'), country),
            'f_TPR': 'r86400',  # Past 24 hours
            'f_JT': job_type_mapping.get(job_type.lower().replace('-', '_'), ''),
            'sortBy': 'DD'  # Sort by date
        }
        
        # Remove empty parameters
        params = {k: v for k, v in params.items() if v}
        
        return f"{base_url}?{urllib.parse.urlencode(params)}"
    
    def _extract_job_data(self) -> List[Dict[str, str]]:
        """Extract job data from the current page"""
        jobs = []
        
        try:
            # Find all job cards
            job_cards = self.driver.find_elements(By.CSS_SELECTOR, ".job-search-card")
            
            for card in job_cards[:Config.MAX_JOBS_PER_SEARCH]:
                try:
                    job_data = self._extract_single_job(card)
                    if job_data:
                        jobs.append(job_data)
                except Exception as e:
                    logger.warning(f"Error extracting job data: {e}")
                    continue
            
        except Exception as e:
            logger.error(f"Error extracting job data: {e}")
        
        return jobs
    
    def _extract_single_job(self, card) -> Optional[Dict[str, str]]:
        """Extract data from a single job card"""
        try:
            # Job title
            title_element = card.find_element(By.CSS_SELECTOR, ".base-search-card__title")
            title = title_element.text.strip()
            
            # Company name
            company_element = card.find_element(By.CSS_SELECTOR, ".base-search-card__subtitle")
            company = company_element.text.strip()
            
            # Location
            location_element = card.find_element(By.CSS_SELECTOR, ".job-search-card__location")
            location = location_element.text.strip()
            
            # Job link
            link_element = card.find_element(By.CSS_SELECTOR, ".base-card__full-link")
            job_link = link_element.get_attribute("href")
            
            # Posted date (if available)
            try:
                date_element = card.find_element(By.CSS_SELECTOR, ".job-search-card__listdate")
                posted_date = date_element.text.strip()
            except:
                posted_date = "Recently"
            
            # Job type/level (if available)
            try:
                level_element = card.find_element(By.CSS_SELECTOR, ".job-search-card__job-insight")
                job_level = level_element.text.strip()
            except:
                job_level = "Not specified"
            
            return {
                'title': title,
                'company': company,
                'location': location,
                'link': job_link,
                'posted_date': posted_date,
                'job_level': job_level,
                'scraped_at': time.strftime('%Y-%m-%d %H:%M:%S')
            }
            
        except Exception as e:
            logger.warning(f"Error extracting single job: {e}")
            return None
    
    def get_job_details(self, job_url: str) -> Dict[str, str]:
        """Get detailed job information"""
        try:
            if not self.driver:
                return {}
            
            self.driver.get(job_url)
            time.sleep(Config.SCRAPING_DELAY)
            
            # Wait for job details to load
            try:
                WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, ".show-more-less-html__markup"))
                )
            except:
                logger.warning("Job details took too long to load")
            
            # Extract job description
            try:
                description_element = self.driver.find_element(
                    By.CSS_SELECTOR, ".show-more-less-html__markup"
                )
                description = description_element.text.strip()
            except:
                description = "Description not available"
            
            # Extract requirements (if available)
            try:
                requirements_element = self.driver.find_element(
                    By.CSS_SELECTOR, ".job-details-jobs-unified-top-card__job-insight"
                )
                requirements = requirements_element.text.strip()
            except:
                requirements = "Requirements not specified"
            
            return {
                'description': description,
                'requirements': requirements,
                'url': job_url
            }
            
        except Exception as e:
            logger.error(f"Error getting job details: {e}")
            return {}
    
    def close(self):
        """Close the WebDriver"""
        if self.driver:
            self.driver.quit()
            logger.info("WebDriver closed")
    
    def __del__(self):
        """Destructor to ensure WebDriver is closed"""
        self.close()

class AlternativeJobScraper:
    """Alternative job scraper using requests and BeautifulSoup"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
    
    def search_jobs_alternative(self, job_category: str, country: str, job_type: str) -> List[Dict[str, str]]:
        """Alternative job search using public job boards"""
        jobs = []
        
        # Search on multiple job boards
        jobs.extend(self._search_indeed(job_category, country, job_type))
        jobs.extend(self._search_glassdoor(job_category, country, job_type))
        
        return jobs[:Config.MAX_JOBS_PER_SEARCH]
    
    def _search_indeed(self, job_category: str, country: str, job_type: str) -> List[Dict[str, str]]:
        """Search jobs on Indeed (simplified)"""
        # This is a simplified implementation
        # In production, you would need to handle Indeed's specific structure
        return []
    
    def _search_glassdoor(self, job_category: str, country: str, job_type: str) -> List[Dict[str, str]]:
        """Search jobs on Glassdoor (simplified)"""
        # This is a simplified implementation
        # In production, you would need to handle Glassdoor's specific structure
        return []
