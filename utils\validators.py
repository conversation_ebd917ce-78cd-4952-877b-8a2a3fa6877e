"""
Input validation utilities
"""
import re
from typing import Optional, List

def validate_email(email: str) -> bool:
    """Validate email format"""
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(pattern, email))

def validate_phone(phone: str) -> bool:
    """Validate phone number format"""
    # Remove spaces, dashes, and parentheses
    cleaned_phone = re.sub(r'[\s\-\(\)]', '', phone)
    # Check if it contains only digits and optional + at the beginning
    pattern = r'^\+?[0-9]{10,15}$'
    return bool(re.match(pattern, cleaned_phone))

def validate_name(name: str) -> bool:
    """Validate name format"""
    if not name or len(name.strip()) < 2:
        return False
    # Allow letters, spaces, hyphens, and apostrophes
    pattern = r"^[a-zA-Z\s\-']+$"
    return bool(re.match(pattern, name.strip()))

def validate_file_extension(filename: str, allowed_extensions: List[str]) -> bool:
    """Validate file extension"""
    if not filename:
        return False
    extension = filename.lower().split('.')[-1]
    return extension in [ext.lower() for ext in allowed_extensions]

def sanitize_filename(filename: str) -> str:
    """Sanitize filename for safe storage"""
    # Remove or replace invalid characters
    sanitized = re.sub(r'[<>:"/\\|?*]', '_', filename)
    # Remove leading/trailing spaces and dots
    sanitized = sanitized.strip(' .')
    # Limit length
    if len(sanitized) > 255:
        name, ext = sanitized.rsplit('.', 1) if '.' in sanitized else (sanitized, '')
        sanitized = name[:250] + ('.' + ext if ext else '')
    return sanitized

def validate_job_category(category: str, valid_categories: List[str]) -> bool:
    """Validate job category selection"""
    return category in valid_categories

def validate_country(country: str, valid_countries: List[str]) -> bool:
    """Validate country selection"""
    return country in valid_countries

def validate_job_type(job_type: str, valid_types: List[str]) -> bool:
    """Validate job type selection"""
    return job_type in valid_types

class ValidationError(Exception):
    """Custom validation error"""
    pass
